#!/bin/bash

# 检查是否指定了安装目录
if [ "$#" -gt 0 ]; then
    INSTALL_DIR=$1
else
    INSTALL_DIR="./install"
fi

echo "构建YOLOv8 RKNN推理库..."
echo "安装目录: $INSTALL_DIR"

# 创建构建目录
mkdir -p build
cd build

# 检查是否在目标开发板上运行
if [ -f "/usr/lib/librknn_api.so" ]; then
    echo "检测到目标设备上的RKNN库，使用系统库..."
    # 配置CMake使用系统库
    cmake .. -DCMAKE_INSTALL_PREFIX=$INSTALL_DIR -DUSE_SYSTEM_LIBS=ON
else
    echo "未检测到系统RKNN库，使用本地库..."
    # 配置CMake使用本地库
    cmake .. -DCMAKE_INSTALL_PREFIX=$INSTALL_DIR -DUSE_SYSTEM_LIBS=OFF
fi

# 编译
make -j$(nproc)

# 安装
make install

cd ..

echo "构建完成。"
echo "请确保安装目录 $INSTALL_DIR 已添加到LD_LIBRARY_PATH环境变量中。"
echo "示例: export LD_LIBRARY_PATH=$INSTALL_DIR/lib:\$LD_LIBRARY_PATH"
echo "运行测试程序: $INSTALL_DIR/bin/test_inference $INSTALL_DIR/share/yolov8_rknn/model/yolov8m.rknn <图像路径> $INSTALL_DIR/share/yolov8_rknn/model/coco_80_labels_list.txt" 