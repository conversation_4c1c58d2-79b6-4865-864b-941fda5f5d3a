#include "../include/yolov8_inference.h"
#include <opencv2/opencv.hpp>
#include <iostream>

int main(int argc, char** argv) {
    // 检查命令行参数
    if (argc < 3) {
        std::cerr << "用法: " << argv[0] << " <rknn模型路径> <输入图像路径> [标签文件路径] [输出图像路径]" << std::endl;
        return -1;
    }

    std::string model_path = argv[1];
    std::string image_path = argv[2];
    std::string label_path = (argc > 3) ? argv[3] : "";
    std::string output_path = (argc > 4) ? argv[4] : "output.jpg";

    // 初始化YOLOv8推理引擎
    yolov8::YOLOv8Inference yolo;
    if (!yolo.initialize(model_path, label_path)) {
        std::cerr << "初始化YOLOv8模型失败!" << std::endl;
        return -1;
    }

    // 读取输入图像
    cv::Mat image = cv::imread(image_path);
    if (image.empty()) {
        std::cerr << "无法读取输入图像: " << image_path << std::endl;
        return -1;
    }

    // 执行推理
    std::vector<yolov8::DetectionResult> results = yolo.inference(image);

    // 输出检测结果
    std::cout << "检测到 " << results.size() << " 个目标:" << std::endl;
    for (const auto& result : results) {
        std::cout << "类别: " << result.class_name 
                  << ", 置信度: " << result.confidence 
                  << ", 位置: [" << result.box.x << ", " << result.box.y 
                  << ", " << result.box.width << ", " << result.box.height << "]" << std::endl;
    }

    // 在图像上绘制检测结果
    yolo.drawResults(image, results);

    // 保存输出图像
    cv::imwrite(output_path, image);
    std::cout << "结果已保存到: " << output_path << std::endl;

    // 释放资源
    yolo.release();

    return 0;
} 