# YOLOv8 RKNN推理库

YOLOv8 RKNN推理库是一个轻量级的C++库，用于在Rockchip NPU上加速YOLOv8模型推理。

## 特性

- 支持YOLOv8模型在Rockchip NPU上推理
- 封装了模型加载、图像预处理、推理和后处理
- 简单的C++ API接口
- 支持RK3562, RK3566, RK3568, RK3576, RK3588, RV1126B等Rockchip平台

## 依赖

- Rockchip RKNN运行时库（librknn_runtime.so）
- Rockchip RGA库（librga.so）
- OpenCV用于图像处理和可视化

## 快速开始

### 编译

项目提供了一个构建脚本，默认会将库安装到`./install`目录：

```bash
./build.sh
```

指定安装目录：

```bash
./build.sh /path/to/install
```

### 在开发主机上编译与测试

如果您在开发主机上编译（而非在Rockchip开发板上），您需要先获取所需的RKNN库文件：

1. 从Rockchip开发板上复制以下文件到项目的`lib`目录：
   - `/usr/lib/librknn_runtime.so`
   - `/usr/lib/librknn_api.so`
   - `/usr/lib/librga.so`

2. 然后执行构建：
   ```bash
   ./build.sh
   ```

### 在Rockchip开发板上编译与运行

如果您直接在Rockchip开发板上编译，脚本会自动检测系统库并使用它们：

1. 确保已安装RKNN Toolkit或RKNN运行时环境
2. 确保已安装OpenCV库
3. 执行构建：
   ```bash
   ./build.sh
   ```

4. 设置库路径并运行测试程序：
   ```bash
   export LD_LIBRARY_PATH=./install/lib:$LD_LIBRARY_PATH
   ./install/bin/test_inference ./install/share/yolov8_rknn/model/yolov8m.rknn ./install/share/yolov8_rknn/model/bus.jpg ./install/share/yolov8_rknn/model/coco_80_labels_list.txt
   ```

## 使用示例

```cpp
#include <yolov8_rknn/yolov8_inference.h>
#include <opencv2/opencv.hpp>
#include <iostream>

int main() {
    // 初始化推理引擎
    yolov8::YOLOv8Inference yolo;
    yolo.initialize("path/to/yolov8.rknn", "path/to/labels.txt");
    
    // 读取图像
    cv::Mat image = cv::imread("path/to/image.jpg");
    
    // 执行推理
    std::vector<yolov8::DetectionResult> results = yolo.inference(image);
    
    // 绘制结果
    yolo.drawResults(image, results);
    
    // 保存输出图像
    cv::imwrite("output.jpg", image);
    
    // 释放资源
    yolo.release();
    
    return 0;
}
```

## API文档

### 主要类和方法

#### `yolov8::YOLOv8Inference`类

- `bool initialize(const std::string& model_path, const std::string& label_path="")` - 初始化推理引擎
- `std::vector<DetectionResult> inference(const cv::Mat& image, float min_confidence=0.5f)` - 对图像执行推理
- `void drawResults(cv::Mat& image, const std::vector<DetectionResult>& results)` - 在图像上绘制检测结果
- `void setNMSThreshold(float nms_threshold)` - 设置NMS阈值
- `cv::Size getInputSize() const` - 获取模型输入大小
- `void release()` - 释放资源

## 常见问题排查

### librknn_runtime.so: 没有那个文件或目录

如果您在运行测试程序时遇到此错误，可能有以下原因：

1. 您在非Rockchip开发板上运行，缺少必要的RKNN运行时库

   解决方案：直接在支持的Rockchip开发板上运行，例如RK3588

2. 系统中没有安装RKNN Toolkit或运行时库

   解决方案：安装RKNN Toolkit或运行时库，可参考Rockchip官方文档

3. 库路径未正确设置

   解决方案：确保正确设置LD_LIBRARY_PATH环境变量，包含RKNN库路径

### RGA错误

如果遇到RGA相关错误，请确保：

1. 开发板上已安装正确版本的librga.so
2. 您使用的图像分辨率和格式被RGA硬件支持

## 注意事项

- 本库针对RK3588平台优化，但也支持其他Rockchip NPU平台
- 确保RKNN模型针对目标平台进行了适当的量化和优化

## 许可证

本项目基于MIT许可证开源。

## 致谢

本项目基于Rockchip的RKNN-Toolkit2和RKNN Model Zoo示例代码。 