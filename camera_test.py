#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
相机测试脚本
用于测试读取两个相机画面并显示
"""

import cv2
import numpy as np
import time
import sys
import os

def list_cameras():
    """列出所有可用的相机设备"""
    print("正在扫描可用的相机设备...")
    available_cameras = []
    
    # 测试前10个设备索引
    for i in range(10):
        cap = cv2.VideoCapture(i)
        if cap.isOpened():
            ret, frame = cap.read()
            if ret and frame is not None:
                height, width = frame.shape[:2]
                print(f"相机 {i}: 分辨率 {width}x{height}")
                available_cameras.append(i)
            cap.release()
        else:
            # 尝试使用V4L2后端
            cap = cv2.VideoCapture(i, cv2.CAP_V4L2)
            if cap.isOpened():
                ret, frame = cap.read()
                if ret and frame is not None:
                    height, width = frame.shape[:2]
                    print(f"相机 {i} (V4L2): 分辨率 {width}x{height}")
                    available_cameras.append(i)
                cap.release()
    
    return available_cameras

def test_single_camera(camera_id, window_name, position=(0, 0)):
    """测试单个相机"""
    print(f"正在测试相机 {camera_id}...")
    
    # 尝试不同的后端
    backends = [cv2.CAP_ANY, cv2.CAP_V4L2, cv2.CAP_GSTREAMER]
    cap = None
    
    for backend in backends:
        try:
            cap = cv2.VideoCapture(camera_id, backend)
            if cap.isOpened():
                ret, frame = cap.read()
                if ret and frame is not None:
                    print(f"相机 {camera_id} 使用后端成功")
                    break
                else:
                    cap.release()
                    cap = None
            else:
                cap = None
        except Exception as e:
            print(f"相机 {camera_id} 后端测试失败: {e}")
            cap = None
    
    if cap is None or not cap.isOpened():
        print(f"无法打开相机 {camera_id}")
        return None
    
    # 设置相机参数
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1920)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 1080)
    cap.set(cv2.CAP_PROP_FPS, 30)
    
    # 获取实际参数
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = cap.get(cv2.CAP_PROP_FPS)
    
    print(f"相机 {camera_id} 参数: {width}x{height} @ {fps}fps")
    
    # 创建窗口
    cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
    cv2.resizeWindow(window_name, 640, 480)
    cv2.moveWindow(window_name, position[0], position[1])
    
    return cap

def main():
    print("=== 相机测试脚本 ===")
    print("按 'q' 退出程序")
    print("按 's' 保存当前帧")
    print("按 'r' 重新初始化相机")
    print()
    
    # 列出可用相机
    available_cameras = list_cameras()
    print(f"发现 {len(available_cameras)} 个可用相机: {available_cameras}")
    
    if len(available_cameras) == 0:
        print("错误: 没有发现可用的相机设备")
        return
    
    # 选择要测试的相机
    if len(available_cameras) >= 2:
        camera_left = available_cameras[0]
        camera_right = available_cameras[1]
        print(f"将测试相机 {camera_left} (左) 和相机 {camera_right} (右)")
    else:
        camera_left = available_cameras[0]
        camera_right = None
        print(f"只发现一个相机，将测试相机 {camera_left}")
    
    # 初始化相机
    cap_left = test_single_camera(camera_left, "左相机", (0, 0))
    cap_right = None
    
    if camera_right is not None:
        cap_right = test_single_camera(camera_right, "右相机", (660, 0))
    
    if cap_left is None:
        print("错误: 无法初始化任何相机")
        return
    
    # 统计信息
    frame_count = 0
    start_time = time.time()
    last_fps_time = start_time
    
    print("\n开始读取相机画面...")
    
    try:
        while True:
            current_time = time.time()
            
            # 读取左相机
            ret_left, frame_left = cap_left.read()
            if not ret_left:
                print("警告: 左相机读取失败")
                frame_left = np.zeros((480, 640, 3), dtype=np.uint8)
                cv2.putText(frame_left, "Left Camera Error", (50, 240), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
            
            # 读取右相机
            frame_right = None
            if cap_right is not None:
                ret_right, frame_right = cap_right.read()
                if not ret_right:
                    print("警告: 右相机读取失败")
                    frame_right = np.zeros((480, 640, 3), dtype=np.uint8)
                    cv2.putText(frame_right, "Right Camera Error", (50, 240), 
                               cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
            
            # 添加信息到画面
            frame_count += 1
            
            # 计算FPS
            if current_time - last_fps_time >= 1.0:
                fps = frame_count / (current_time - start_time)
                last_fps_time = current_time
                print(f"FPS: {fps:.1f}, 总帧数: {frame_count}")
            
            # 在画面上显示信息
            info_text = f"Frame: {frame_count}, Time: {current_time - start_time:.1f}s"
            cv2.putText(frame_left, info_text, (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            cv2.putText(frame_left, f"Camera {camera_left}", (10, 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
            
            if frame_right is not None:
                cv2.putText(frame_right, info_text, (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                cv2.putText(frame_right, f"Camera {camera_right}", (10, 60), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
            
            # 显示画面
            cv2.imshow("左相机", frame_left)
            if frame_right is not None:
                cv2.imshow("右相机", frame_right)
            
            # 处理按键
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                print("用户退出")
                break
            elif key == ord('s'):
                # 保存当前帧
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                cv2.imwrite(f"left_camera_{timestamp}.jpg", frame_left)
                if frame_right is not None:
                    cv2.imwrite(f"right_camera_{timestamp}.jpg", frame_right)
                print(f"已保存图片: {timestamp}")
            elif key == ord('r'):
                # 重新初始化相机
                print("重新初始化相机...")
                if cap_left:
                    cap_left.release()
                if cap_right:
                    cap_right.release()
                
                time.sleep(1)
                
                cap_left = test_single_camera(camera_left, "左相机", (0, 0))
                if camera_right is not None:
                    cap_right = test_single_camera(camera_right, "右相机", (660, 0))
                
                if cap_left is None:
                    print("重新初始化失败")
                    break
    
    except KeyboardInterrupt:
        print("\n程序被中断")
    
    except Exception as e:
        print(f"程序异常: {e}")
    
    finally:
        # 清理资源
        if cap_left:
            cap_left.release()
        if cap_right:
            cap_right.release()
        cv2.destroyAllWindows()
        
        # 显示统计信息
        total_time = time.time() - start_time
        avg_fps = frame_count / total_time if total_time > 0 else 0
        print(f"\n=== 测试完成 ===")
        print(f"总运行时间: {total_time:.1f}秒")
        print(f"总帧数: {frame_count}")
        print(f"平均FPS: {avg_fps:.1f}")

if __name__ == "__main__":
    main()
