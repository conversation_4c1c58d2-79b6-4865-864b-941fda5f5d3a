<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <style>
    .point { fill: #ffffff; stroke: #5d9cec; stroke-width: 2; }
    .line { stroke: #5d9cec; stroke-width: 3; stroke-linecap: round; stroke-linejoin: round; }
  </style>
  
  <!-- 折线 -->
  <polyline points="36,44 64,64 48,80 76,88" class="line" fill="none" />
  
  <!-- 点 -->
  <circle cx="36" cy="44" r="5" class="point" />
  <circle cx="64" cy="64" r="5" class="point" />
  <circle cx="48" cy="80" r="5" class="point" />
  <circle cx="76" cy="88" r="5" class="point" />
</svg> 