<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <style>
    .point { fill: #5d9cec; stroke: #ffffff; stroke-width: 2; }
    .wave { stroke: #ffffff; stroke-width: 3; fill: none; stroke-linecap: round; }
    .line { stroke: #5d9cec; stroke-width: 1.5; }
    .ref-line { stroke: #ffffff; stroke-width: 1; stroke-opacity: 0.3; }
  </style>
  
  <!-- 背景矩形，确保图标有足够大小 -->
  <rect x="24" y="24" width="80" height="80" rx="16" ry="16" fill="none" />
  
  <!-- 参考底线 -->
  <line x1="40" y1="90" x2="88" y2="90" class="ref-line" />
  
  <!-- 斜线表示区域 - 使用平行的斜线，确保在白线下方 -->
  <line x1="44" y1="90" x2="48" y2="50" class="line" />
  <line x1="52" y1="90" x2="55" y2="63" class="line" />
  <line x1="60" y1="90" x2="62" y2="74" class="line" />
  <line x1="68" y1="90" x2="71" y2="68" class="line" />
  <line x1="76" y1="90" x2="81" y2="50" class="line" />
  <line x1="84" y1="90" x2="88" y2="50" class="line" />
  
  <!-- 剖面曲线（两端直线，中间下凹） -->
  <path d="M40,50 L47,50 C54,50 56,75 64,75 C72,75 74,50 81,50 L88,50" class="wave" />
  
  <!-- 端点 -->
  <circle cx="40" cy="50" r="4" class="point" />
  <circle cx="88" cy="50" r="4" class="point" />
</svg> 