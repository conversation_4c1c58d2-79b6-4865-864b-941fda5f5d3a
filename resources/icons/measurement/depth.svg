<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <style>
    .point { fill: #ffffff; stroke: #5d9cec; stroke-width: 2; }
    .line { stroke: #5d9cec; stroke-width: 3; stroke-linecap: round; }
    .arrow { fill: #5d9cec; }
    .face { fill: #5d9cec; fill-opacity: 0.15; stroke: #5d9cec; stroke-width: 2; }
    .dashed { stroke: #5d9cec; stroke-width: 2; stroke-dasharray: 4,3; }
  </style>
  
  <!-- 立体透视三角形 -->
  <polygon points="25,40 95,25 65,55" class="face" stroke-linejoin="round" />
  
  <!-- 三角形边缘 -->
  <line x1="25" y1="40" x2="95" y2="25" class="line" />
  <line x1="25" y1="40" x2="65" y2="55" class="line" />
  <line x1="95" y1="25" x2="65" y2="55" class="line" />
  
  <!-- 从中心点指向下方的垂直深度线 -->
  <line x1="62" y1="40" x2="62" y2="90" class="line" />
  
  <!-- 箭头 -->
  <polygon points="54,82 62,90 70,82" class="arrow" />
  
  <!-- 点 -->
  <circle cx="25" cy="40" r="5" class="point" />
  <circle cx="95" cy="25" r="5" class="point" />
  <circle cx="65" cy="55" r="5" class="point" />
  <circle cx="62" cy="40" r="5" class="point" />
</svg> 