<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <style>
    .point { fill: #ffffff; stroke: #5d9cec; stroke-width: 2; }
    .line { stroke: #5d9cec; stroke-width: 3; stroke-linecap: round; stroke-linejoin: round; }
    .hatch { stroke: #5d9cec; stroke-width: 2; }
  </style>
  
  <!-- 斜线填充 -->
  <line x1="36" y1="36" x2="92" y2="92" class="hatch" />
  <line x1="50" y1="36" x2="92" y2="78" class="hatch" />
  <line x1="64" y1="36" x2="92" y2="64" class="hatch" />
  <line x1="78" y1="36" x2="92" y2="50" class="hatch" />
  <line x1="36" y1="50" x2="78" y2="92" class="hatch" />
  <line x1="36" y1="64" x2="64" y2="92" class="hatch" />
  <line x1="36" y1="78" x2="50" y2="92" class="hatch" />
  
  <!-- 点 -->
  <circle cx="36" cy="36" r="5" class="point" />
  <circle cx="92" cy="36" r="5" class="point" />
  <circle cx="92" cy="92" r="5" class="point" />
  <circle cx="36" cy="92" r="5" class="point" />
</svg> 