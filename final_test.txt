初始化日志系统...
应用程序路径: "/home/<USER>/App/Qt/SmartScope"
当前工作目录: "/home/<USER>/App/Qt/SmartScope"
使用日志目录: "/home/<USER>/App/Qt/SmartScope/logs"
日志文件路径: "/home/<USER>/App/Qt/SmartScope/logs/app.log"
日志文件可写
Logger::init - 开始初始化日志系统
Logger::init - 日志文件路径: "/home/<USER>/App/Qt/SmartScope/logs/app.log"
Logger::init - 日志级别: "INFO"
Logger::init - 启用控制台输出: true
Logger::init - 启用文件输出: false
Logger::init - 文件输出已禁用，初始化完成
日志系统初始化成功，仅使用控制台输出
[32m 2025-05-27 10:40:09.057 [INFO] main.cpp:67 initLogger() 应用程序启动成功 [0m
初始化配置管理器...
ConfigManager: 创建配置管理器实例
ConfigManager: 初始化配置管理器
ConfigManager: 初始化完成
配置管理器初始化成功
找到配置文件: "/home/<USER>/App/Qt/SmartScope/config.toml"
加载配置文件: "/home/<USER>/App/Qt/SmartScope/config.toml"
ConfigManager: 加载TOML配置文件: "/home/<USER>/App/Qt/SmartScope/config.toml"
ConfigManager: TOML配置文件加载成功
配置文件加载成功
解析到的左相机名称: ("cameraL", "Web Camera 2Ks")
解析到的右相机名称: ("cameraR", "USB Camera")
[32m 2025-05-27 10:40:09.058 [INFO] main.cpp:131 initConfig() 配置管理器初始化成功 [0m
当前目录： "/home/<USER>/App/Qt/SmartScope"
检查模型文件： "/home/<USER>/App/Qt/SmartScope/models/yolov8m.rknn" 存在： true
检查标签文件： "/home/<USER>/App/Qt/SmartScope/models/coco_80_labels_list.txt" 存在： true
检查模型文件(build目录)： "/home/<USER>/App/Qt/SmartScope/build/models/yolov8m.rknn" 存在： false
检查标签文件(build目录)： "/home/<USER>/App/Qt/SmartScope/build/models/coco_80_labels_list.txt" 存在： false
初始化YOLOv8服务...
YOLOv8模型文件不存在: "/home/<USER>/App/Qt/SmartScope/../models/yolov8m.rknn"
尝试使用替代路径: "/home/<USER>/App/Qt/SmartScope/models/yolov8m.rknn"
YOLOv8标签文件不存在: "/home/<USER>/App/Qt/SmartScope/../models/coco_80_labels_list.txt"
尝试使用替代路径: "/home/<USER>/App/Qt/SmartScope/models/coco_80_labels_list.txt"
[YOLOv8Service] "开始初始化YOLOv8Service，模型路径: /home/<USER>/App/Qt/SmartScope/models/yolov8m.rknn，标签路径: /home/<USER>/App/Qt/SmartScope/models/coco_80_labels_list.txt"
[YOLOv8Service] "正在初始化检测器 1/3..."
[YOLOv8Service] "检测器 1 初始化成功"
[YOLOv8Service] "正在初始化检测器 2/3..."
[YOLOv8Service] "检测器 2 初始化成功"
[YOLOv8Service] "正在初始化检测器 3/3..."
[YOLOv8Service] 服务已创建，线程池最大线程数: 3
model input num: 1, output num: 9
input tensors:
  index=0, name=images, n_dims=4, dims=[1, 640, 640, 3], n_elems=1228800, size=1228800, fmt=NHWC, type=INT8, qnt_type=AFFINE, zp=-128, scale=0.003922
output tensors:
  index=0, name=424, n_dims=4, dims=[1, 64, 80, 80], n_elems=409600, size=409600, fmt=NCHW, type=INT8, qnt_type=AFFINE, zp=-54, scale=0.093129
  index=1, name=onnx::ReduceSum_432, n_dims=4, dims=[1, 80, 80, 80], n_elems=512000, size=512000, fmt=NCHW, type=INT8, qnt_type=AFFINE, zp=-128, scale=0.003389
  index=2, name=437, n_dims=4, dims=[1, 1, 80, 80], n_elems=6400, size=6400, fmt=NCHW, type=INT8, qnt_type=AFFINE, zp=-128, scale=0.003390
  index=3, name=444, n_dims=4, dims=[1, 64, 40, 40], n_elems=102400, size=102400, fmt=NCHW, type=INT8, qnt_type=AFFINE, zp=-44, scale=0.083778
  index=4, name=onnx::ReduceSum_452, n_dims=4, dims=[1, 80, 40, 40], n_elems=128000, size=128000, fmt=NCHW, type=INT8, qnt_type=AFFINE, zp=-128, scale=0.003689
  index=5, name=456, n_dims=4, dims=[1, 1, 40, 40], n_elems=1600, size=1600, fmt=NCHW, type=INT8, qnt_type=AFFINE, zp=-128, scale=0.003690
  index=6, name=463, n_dims=4, dims=[1, 64, 20, 20], n_elems=25600, size=25600, fmt=NCHW, type=INT8, qnt_type=AFFINE, zp=-51, scale=0.072593
  index=7, name=onnx::ReduceSum_471, n_dims=4, dims=[1, 80, 20, 20], n_elems=32000, size=32000, fmt=NCHW, type=INT8, qnt_type=AFFINE, zp=-128, scale=0.003822
  index=8, name=475, n_dims=4, dims=[1, 1, 20, 20], n_elems=400, size=400, fmt=NCHW, type=INT8, qnt_type=AFFINE, zp=-128, scale=0.003922
model is NHWC input fmt
model input height=640, width=640, channel=3
模型输入大小: 640x640x3
model input num: 1, output num: 9
input tensors:
  index=0, name=images, n_dims=4, dims=[1, 640, 640, 3], n_elems=1228800, size=1228800, fmt=NHWC, type=INT8, qnt_type=AFFINE, zp=-128, scale=0.003922
output tensors:
  index=0, name=424, n_dims=4, dims=[1, 64, 80, 80], n_elems=409600, size=409600, fmt=NCHW, type=INT8, qnt_type=AFFINE, zp=-54, scale=0.093129
  index=1, name=onnx::ReduceSum_432, n_dims=4, dims=[1, 80, 80, 80], n_elems=512000, size=512000, fmt=NCHW, type=INT8, qnt_type=AFFINE, zp=-128, scale=0.003389
  index=2, name=437, n_dims=4, dims=[1, 1, 80, 80], n_elems=6400, size=6400, fmt=NCHW, type=INT8, qnt_type=AFFINE, zp=-128, scale=0.003390
  index=3, name=444, n_dims=4, dims=[1, 64, 40, 40], n_elems=102400, size=102400, fmt=NCHW, type=INT8, qnt_type=AFFINE, zp=-44, scale=0.083778
  index=4, name=onnx::ReduceSum_452, n_dims=4, dims=[1, 80, 40, 40], n_elems=128000, size=128000, fmt=NCHW, type=INT8, qnt_type=AFFINE, zp=-128, scale=0.003689
  index=5, name=456, n_dims=4, dims=[1, 1, 40, 40], n_elems=1600, size=1600, fmt=NCHW, type=INT8, qnt_type=AFFINE, zp=-128, scale=0.003690
  index=6, name=463, n_dims=4, dims=[1, 64, 20, 20], n_elems=25600, size=25600, fmt=NCHW, type=INT8, qnt_type=AFFINE, zp=-51, scale=0.072593
  index=7, name=onnx::ReduceSum_471, n_dims=4, dims=[1, 80, 20, 20], n_elems=32000, size=32000, fmt=NCHW, type=INT8, qnt_type=AFFINE, zp=-128, scale=0.003822
  index=8, name=475, n_dims=4, dims=[1, 1, 20, 20], n_elems=400, size=400, fmt=NCHW, type=INT8, qnt_type=AFFINE, zp=-128, scale=0.003922
model is NHWC input fmt
model input height=640, width=640, channel=3
模型输入大小: 640x640x3
model input num: 1, output num: 9
input tensors:
  index=0, name=images, n_dims=4, dims=[1, 640, 640, 3], n_elems=1228800, size=1228800, fmt=NHWC, type=INT8, qnt_type=AFFINE, zp=-128, scale=0.003922
output tensors:
  index=0, name=424, n_dims=4, dims=[1, 64, 80, 80], n_elems=409600, size=409600, fmt=NCHW, type=INT8, qnt_type=AFFINE, zp=-54, scale=0.093129
  index=1, name=onnx::ReduceSum_432, n_dims=4, dims=[1, 80, 80, 80], n_elems=512000, size=512000, fmt=NCHW, type=INT8, qnt_type=AFFINE, zp=-128, scale=0.003389
  index=2, name=437, n_dims=4, dims=[1, 1, 80, 80], n_elems=6400, size=6400, fmt=NCHW, type=INT8, qnt_type=AFFINE, zp=-128, scale=0.003390
  index=3, name=444, n_dims=4, dims=[1, 64, 40, 40], n_elems=102400, size=102400, fmt=NCHW, type=INT8, qnt_ty[YOLOv8Service] "检测器 3 初始化成功"
[YOLOv8Service] "YOLOv8Service初始化完成，已创建3个检测器实例"
YOLOv8服务初始化成功
[32m 2025-05-27 10:40:09.234 [INFO] main.cpp:176 initYOLOv8Service() YOLOv8服务初始化成功 [0m
YOLOv8服务初始化结果： 成功
YOLOv8服务是否已初始化： true
初始化STM32通信管理器...
[32m 2025-05-27 10:40:09.234 [INFO] stm32_communication_manager.cpp:62 STM32CommunicationManager() STM32通信管理器创建完成 [0m
[32m 2025-05-27 10:40:09.234 [INFO] stm32_communication_manager.cpp:73 initialize() 初始化STM32通信管理器... [0m
[32m 2025-05-27 10:40:09.294 [INFO] stm32_communication_manager.cpp:274 initHidCommunication() STM32 HID设备连接成功: eddysun - eddysun15 [0m
[32m 2025-05-27 10:40:09.294 [INFO] stm32_communication_manager.cpp:88 initialize() STM32通信管理器初始化成功 [0m
STM32通信管理器初始化成功
[32m 2025-05-27 10:40:09.294 [INFO] main.cpp:202 initSTM32CommunicationManager() STM32通信管理器初始化成功 [0m
初始化LED控制器...
[32m 2025-05-27 10:40:09.294 [INFO] led_controller.cpp:83 initialize() LED控制器初始化完成，共 11 个亮度级别 [0m
[32m 2025-05-27 10:40:09.294 [INFO] led_controller.cpp:51 LedController() LED控制器初始化完成 [0m
LED控制器连接成功
[32m 2025-05-27 10:40:09.294 [INFO] main.cpp:231 initLEDController() LED控制器初始化成功 [0m
初始化温度控制器...
[32m 2025-05-27 10:40:09.294 [INFO] temperature_controller.cpp:35 TemperatureController() 温度传感器控制器初始化完成 [0m
温度控制器连接成功
温度控制器初始化成功，当前温度: 25 °C
[32m 2025-05-27 10:40:09.294 [INFO] main.cpp:269 initTemperatureController() 温度控制器初始化成功 [0m
[32m 2025-05-27 10:40:09.314 [INFO] mainwindow.cpp:88 setupUi() 屏幕分辨率: 1920x1200 [0m
[32m 2025-05-27 10:40:09.314 [INFO] mainwindow.cpp:89 setupUi() 窗口大小: 1920x1200 [0m
[32m 2025-05-27 10:40:09.314 [INFO] mainwindow.cpp:94 setupUi() 当前应用字体: "WenQuanYi Zen Hei" [0m
BasePage UI初始化完成
[33m 2025-05-27 10:40:09.316 [WARNING] home_page.cpp:431 initContent() 未找到状态栏中的路径选择器 [0m
[32m 2025-05-27 10:40:09.316 [INFO] home_page.cpp:434 initContent() 主页内容初始化完成 [0m
[32m 2025-05-27 10:40:09.316 [INFO] home_page.cpp:1179 createAdjustmentPanel() 创建调节面板... [0m
[32m 2025-05-27 10:40:09.320 [INFO] home_page.cpp:1467 createAdjustmentPanel() 调节面板创建完成 [0m
[33m 2025-05-27 10:40:09.320 [WARNING] home_page.cpp:203 initToolBarButtons() 无法获取工具栏，无法初始化工具栏按钮 [0m
[32m 2025-05-27 10:40:09.320 [INFO] home_page.cpp:1179 createAdjustmentPanel() 创建调节面板... [0m
[32m 2025-05-27 10:40:09.323 [INFO] home_page.cpp:1467 createAdjustmentPanel() 调节面板创建完成 [0m
[33m 2025-05-27 10:40:09.323 [WARNING] home_page.cpp:203 initToolBarButtons() 无法获取工具栏，无法初始化工具栏按钮 [0m
[32m 2025-05-27 10:40:09.323 [INFO] keyboard_listener.cpp:19 KeyboardListener() 键盘监听器初始化完成 [0m
[32m 2025-05-27 10:40:09.323 [INFO] keyboard_listener.cpp:44 registerKeyHandler() 注册按键处理函数：按键=16777272，上下文=367846830608 [0m
[32m 2025-05-27 10:40:09.323 [INFO] keyboard_listener.cpp:44 registerKeyHandler() 注册按键处理函数：按键=16777275，上下文=367846830608 [0m
[32m 2025-05-27 10:40:09.323 [INFO] home_page.cpp:144 HomePage() 已连接YOLOv8检测完成信号 [0m
BasePage UI初始化完成
[32m 2025-05-27 10:40:09.342 [INFO] preview_page.cpp:889 initContent() 预览页面内容初始化完成 [0m
[32m 2025-05-27 10:40:09.349 [INFO] preview_page.cpp:504 ImagePreviewDialog() 图片预览对话框初始化完成 [0m
[32m 2025-05-27 10:40:09.349 [INFO] preview_page.cpp:801 PreviewPage() 预览页面构造完成 [0m
BasePage UI初始化完成
报告页面内容初始化完成
BasePage UI初始化完成
注释页面内容初始化完成
BasePage UI初始化完成
[32m 2025-05-27 10:40:09.350 [INFO] stereo_calibration_helper.cpp:14 StereoCalibrationHelper() StereoCalibrationHelper instance created. [0m
[32m 2025-05-27 10:40:09.350 [INFO] measurement_calculator.cpp:13 MeasurementCalculator() 创建测量计算器实例 [0m
[32m 2025-05-27 10:40:09.350 [INFO] measurement_renderer.cpp:27 MeasurementRenderer() MeasurementRenderer 已创建 [0m
[32m 2025-05-27 10:40:09.350 [INFO] measurement_renderer.cpp:47 MeasurementRenderer() 找到字体文件: /usr/share/fonts/truetype/wqy/wqy-zenhei.ttc [0m
[32m 2025-05-27 10:40:09.350 [INFO] measurement_renderer.cpp:54 MeasurementRenderer() FreeType 字体加载成功 [0m
[32m 2025-05-27 10:40:09.351 [INFO] point_cloud_generator.cpp:7 PointCloudGenerator() PointCloudGenerator created. [0m
[32m 2025-05-27 10:40:09.351 [INFO] profile_chart_manager.cpp:13 ProfileChartManager() 创建剖面图表管理器 [0m
[32m 2025-05-27 10:40:09.351 [INFO] measurement_page.cpp:193 initContent() 初始化测量页面内容 [0m
QLayout: Attempting to add QLayout "" to QWidget "pageContent", which already has a layout
[32m 2025-05-27 10:40:09.351 [INFO] point_cloud_gl_widget.cpp:74 PointCloudGLWidget() 初始化点云渲染控件 [0m
[32m 2025-05-27 10:40:09.351 [INFO] point_cloud_gl_widget.cpp:101 PointCloudGLWidget() 点云控件初始化完成，默认设置为Z轴负方向视角 [0m
[32m 2025-05-27 10:40:09.360 [INFO] point_cloud_gl_widget.cpp:117 initializeGL() 初始化OpenGL环境 [0m
[32m 2025-05-27 10:40:09.360 [INFO] point_cloud_gl_widget.cpp:133 initializeGL() OpenGL深度测试和点渲染设置已应用 [0m
[32m 2025-05-27 10:40:09.360 [INFO] point_cloud_gl_widget.cpp:148 initShaders() 初始化OpenGL着色器 [0m
[32m 2025-05-27 10:40:09.377 [INFO] point_cloud_gl_widget.cpp:164 setupVertexBuffers() 设置OpenGL顶点缓冲区 [0m
[32m 2025-05-27 10:40:09.383 [INFO] point_cloud_gl_widget.cpp:899 initAxes() 坐标轴初始化完成 [0m
[32m 2025-05-27 10:40:09.398 [INFO] profile_chart_manager.cpp:88 applyDarkTheme() 已应用深色主题到剖面图表 [0m
[32m 2025-05-27 10:40:09.398 [INFO] profile_chart_manager.cpp:41 initializeChart() 剖面图表初始化成功 [0m
[32m 2025-05-27 10:40:09.399 [INFO] measurement_page.cpp:372 initContent() 3D测量页面内容初始化完成 [0m
[32m 2025-05-27 10:40:09.399 [INFO] measurement_menu.cpp:165 setupUI() 3D测量菜单栏UI设置完成 [0m
[32m 2025-05-27 10:40:09.399 [INFO] measurement_menu.cpp:136 MeasurementMenuBar() 3D测量菜单栏构造完成 [0m
[32m 2025-05-27 10:40:09.401 [INFO] measurement_menu.cpp:30 MeasurementMenuButton() 创建撤回按钮，确保使用标准点击处理 [0m
[32m 2025-05-27 10:40:09.403 [INFO] measurement_page.cpp:520 updateLayout() 更新菜单栏位置: (110, 1040) 尺寸: 1700x160 [0m
[32m 2025-05-27 10:40:09.403 [INFO] measurement_page.cpp:530 updateLayout() 更新左区域比例: leftWidth=100, totalWidth=100, leftAreaRatio=1.000 [0m
[32m 2025-05-27 10:40:09.403 [INFO] measurement_page.cpp:493 createMenuBar() 3D测量菜单栏创建完成 [0m
[32m 2025-05-27 10:40:09.403 [INFO] measurement_page.cpp:1623 initMeasurementFeatures() 初始化3D测量功能 [0m
[32m 2025-05-27 10:40:09.403 [INFO] measurement_object.cpp:183 MeasurementManager() 创建测量管理器 [0m
[32m 2025-05-27 10:40:09.403 [INFO] measurement_state_manager.cpp:9 MeasurementStateManager() 创建测量状态管理器 [0m
[32m 2025-05-27 10:40:09.403 [INFO] image_interaction_manager.cpp:18 ImageInteractionManager() 创建图像交互管理器 [0m
[32m 2025-05-27 10:40:09.403 [INFO] measurement_page.cpp:1636 initMeasurementFeatures() 初始化图像交互管理器 [0m
[32m 2025-05-27 10:40:09.403 [INFO] image_interaction_manager.cpp:34 initialize() 初始化图像交互管理器 [0m
[32m 2025-05-27 10:40:09.403 [INFO] image_interaction_manager.cpp:53 initialize() 图像交互管理器初始化完成 [0m
[32m 2025-05-27 10:40:09.403 [INFO] measurement_page.cpp:1647 initMeasurementFeatures() 图像交互管理器初始化成功 [0m
[32m 2025-05-27 10:40:09.403 [INFO] measurement_page.cpp:1673 initMeasurementFeatures() 初始化：默认禁用左图区域点击，仅在长度测量模式时启用 [0m
BasePage UI初始化完成
[32m 2025-05-27 10:40:09.403 [INFO] measurement_type_selection_page.cpp:138 MeasurementTypeSelectionPage() 创建测量类型选择页面 [0m
[32m 2025-05-27 10:40:09.417 [INFO] measurement_page.cpp:1426 updateUIBasedOnMeasurementState() 完成按钮可见性设置为：隐藏 [0m
[32m 2025-05-27 10:40:09.417 [INFO] measurement_page.cpp:1775 initMeasurementFeatures() 3D测量功能初始化完成 [0m
[32m 2025-05-27 10:40:09.417 [INFO] profile_chart_manager.cpp:295 updateControlsVisibility() 无剖面测量，隐藏剖面图表 [0m
[32m 2025-05-27 10:40:09.417 [INFO] measurement_page.cpp:88 MeasurementPage() 找到相机参数基础路径: ./camera_parameters [0m
[32m 2025-05-27 10:40:09.417 [INFO] stereo_calibration_helper.cpp:43 loadParameters() 开始加载相机参数... [0m
[32m 2025-05-27 10:40:09.417 [INFO] stereo_calibration_helper.cpp:44 loadParameters() 使用基础路径: ./camera_parameters [0m
[32m 2025-05-27 10:40:09.417 [INFO] stereo_calibration_helper.cpp:45 loadParameters() 当前工作目录: /home/<USER>/App/Qt/SmartScope [0m
[32m 2025-05-27 10:40:09.417 [INFO] stereo_calibration_helper.cpp:68 loadParameters() 使用相机参数文件:
左相机: ./camera_parameters/camera0_intrinsics.dat
右相机: ./camera_parameters/camera1_intrinsics.dat
旋转平移: ./camera_parameters/camera1_rot_trans.dat [0m
[32m 2025-05-27 10:40:09.417 [INFO] stereo_calibration_helper.cpp:73 loadParameters() 开始读取相机参数文件... [0m
[32m 2025-05-27 10:40:09.417 [INFO] stereo_calibration_helper.cpp:184 parseIntrinsicsFile() 成功解析相机内参文件: ./camera_parameters/camera0_intrinsics.dat [0m
[32m 2025-05-27 10:40:09.418 [INFO] stereo_calibration_helper.cpp:184 parseIntrinsicsFile() 成功解析相机内参文件: ./camera_parameters/camera1_intrinsics.dat [0m
[32m 2025-05-27 10:40:09.418 [INFO] stereo_calibration_helper.cpp:238 parseRotTransFile() 成功解析旋转平移参数文件: ./camera_parameters/camera1_rot_trans.dat [0m
[32m 2025-05-27 10:40:09.418 [INFO] stereo_calibration_helper.cpp:107 loadParameters() 相机参数加载成功 [0m
[32m 2025-05-27 10:40:09.418 [INFO] stereo_calibration_helper.cpp:474 printMatrixContent() 左相机内参矩阵 [3x3, type:6]:
[ 898.3346, 0.0000, 340.2722 ]
[ 0.0000, 898.1238, 671.8856 ]
[ 0.0000, 0.0000, 1.0000 ]
 [0m
[32m 2025-05-27 10:40:09.418 [INFO] stereo_calibration_helper.cpp:474 printMatrixContent() 左相机畸变系数 [1x5, type:6]:
[ -0.3515, 0.1385, 0.0002, 0.0009, -0.0302 ]
 [0m
[32m 2025-05-27 10:40:09.418 [INFO] stereo_calibration_helper.cpp:474 printMatrixContent() 右相机内参矩阵 [3x3, type:6]:
[ 912.8658, 0.0000, 331.7446 ]
[ 0.0000, 912.3004, 618.8318 ]
[ 0.0000, 0.0000, 1.0000 ]
 [0m
[32m 2025-05-27 10:40:09.418 [INFO] stereo_calibration_helper.cpp:474 printMatrixContent() 右相机畸变系数 [1x5, type:6]:
[ -0.3583, 0.1652, -0.0001, -0.0020, -0.0523 ]
 [0m
[32m 2025-05-27 10:40:09.418 [INFO] stereo_calibration_helper.cpp:474 printMatrixContent() 旋转矩阵 [3x3, type:6]:
[ 1.0000, -0.0053, 0.0053 ]
[ 0.0054, 1.0000, -0.0061 ]
[ -0.0052, 0.0061, 1.0000 ]
 [0m
[32m 2025-05-27 10:40:09.418 [INFO] stereo_calibration_helper.cpp:474 printMatrixContent() 平移向量 [3x1, type:6]:
[ -2.0531 ]
[ -0.0878 ]
[ 0.0629 ]
 [0m
[32m 2025-05-27 10:40:09.418 [INFO] measurement_page.cpp:97 MeasurementPage() 相机参数加载成功 (通过 Helper) [0m
[32m 2025-05-27 10:40:09.418 [INFO] stereo_calibration_helper.cpp:244 initializeRectification() 初始化立体校正，图像尺寸: 720x1280 [0m
[32m 2025-05-27 10:40:09.418 [INFO] stereo_calibration_helper.cpp:274 initializeRectification() 开始计算立体校正参数... [0m
[32m 2025-05-27 10:40:09.418 [INFO] stereo_calibration_helper.cpp:284 initializeRectification() 立体校正参数计算成功 [0m
[32m 2025-05-27 10:40:09.418 [INFO] stereo_calibration_helper.cpp:285 initializeRectification() 左相机有效区域ROI: (0, 0, 720, 1280) [0m
[32m 2025-05-27 10:40:09.418 [INFO] stereo_calibration_helper.cpp:287 initializeRectification() 右相机有效区域ROI: (0, 0, 720, 1280) [0m
[32m 2025-05-27 10:40:09.418 [INFO] stereo_calibration_helper.cpp:474 printMatrixContent() 重投影矩阵 Q [4x4, type:6]:
[ 1.0000, 0.0000, 0.0000, -345.5841 ]
[ 0.0000, 1.0000, 0.0000, -648.9067 ]
[ 0.0000, 0.0000, 0.0000, 905.2121 ]
[ 0.0000, 0.0000, 0.4864, 0.0000 ]
 [0m
[32m 2025-05-27 10:40:09.418 [INFO] stereo_calibration_helper.cpp:297 initializeRectification() 开始计算重映射表... [0m
[32m 2025-05-27 10:40:09.428 [INFO] stereo_calibration_helper.cpp:308 initializeRectification() 重映射表计算成功 [0m
[32m 2025-05-27 10:40:09.428 [INFO] stereo_calibration_helper.cpp:325 initializeRectification() 立体校正参数和重映射表初始化成功 [0m
[32m 2025-05-27 10:40:09.428 [INFO] measurement_page.cpp:100 MeasurementPage() 立体校正参数初始化成功 (通过 Helper) [0m
pe=AFFINE, zp=-44, scale=0.083778
  index=4, name=onnx::ReduceSum_452, n_dims=4, dims=[1, 80, 40, 40], n_elems=128000, size=128000, fmt=NCHW, type=INT8, qnt_type=AFFINE, zp=-128, scale=0.003689
  index=5, name=456, n_dims=4, dims=[1, 1, 40, 40], n_elems=1600, size=1600, fmt=NCHW, type=INT8, qnt_type=AFFINE, zp=-128, scale=0.003690
  index=6, name=463, n_dims=4, dims=[1, 64, 20, 20], n_elems=25600, size=25600, fmt=NCHW, type=INT8, qnt_type=AFFINE, zp=-51, scale=0.072593
  index=7, name=onnx::ReduceSum_471, n_dims=4, dims=[1, 80, 20, 20], n_elems=32000, size=32000, fmt=NCHW, type=INT8, qnt_type=AFFINE, zp=-128, scale=0.003822
  index=8, name=475, n_dims=4, dims=[1, 1, 20, 20], n_elems=400, size=400, fmt=NCHW, type=INT8, qnt_type=AFFINE, zp=-128, scale=0.003922
model is NHWC input fmt
model input height=640, width=640, channel=3
模型输入大小: 640x640x3
arm_release_ver: g13p0-01eac0, rk_so_ver: 10
Loading model from: /home/<USER>/App/Qt/SmartScope/models/stereo_model.onnx
Creating optimized session with 8 threads...
Getting input info...
Number of inputs: 2
Input 0: input1
Input 1: input2
Getting output info...
Number of outputs: 1
Output 0: output
Updating input shape...
Updated input shape: [1, 3, 736, 736]
Model loaded successfully with optimized settings
Updating input shape...
Updated input shape: [1, 3, 256, 256]
[32m 2025-05-27 10:40:36.888 [INFO] inference_service.cpp:375 logInfo() 推理服务初始化成功 (使用极速模式: 256×256) [0m
[32m 2025-05-27 10:40:36.889 [INFO] measurement_page.cpp:138 MeasurementPage() 推理服务初始化成功, 使用模型: /home/<USER>/App/Qt/SmartScope/models/stereo_model.onnx [0m
[32m 2025-05-27 10:40:36.889 [INFO] measurement_page.cpp:149 MeasurementPage() 推理完成信号已成功连接到handleInferenceResult槽函数 [0m
[32m 2025-05-27 10:40:36.889 [INFO] point_cloud_renderer.cpp:19 PointCloudRenderer() PointCloudRenderer 已创建 [0m
[32m 2025-05-27 10:40:36.889 [INFO] page_manager.cpp:60 setupPages() 页面管理器初始化完成 [0m
[32m 2025-05-27 10:40:36.893 [INFO] navigation_bar.cpp:116 setupUi() 导航栏UI设置完成 [0m
[32m 2025-05-27 10:40:36.899 [INFO] navigation_bar.cpp:42 NavigationBar() 导航栏构造完成 [0m
[32m 2025-05-27 10:40:36.909 [INFO] statusbar.cpp:1642 PathSelector() 路径选择器初始化完成，根目录: /home/<USER>/data，默认路径: /home/<USER>/data/CAM [0m
[32m 2025-05-27 10:40:36.910 [INFO] statusbar.cpp:1805 calculateOptimalWidth() 计算状态栏最佳宽度 (占满屏幕): 1920 [0m
[32m 2025-05-27 10:40:36.910 [INFO] statusbar.cpp:1844 adjustSizeToContent() 调整状态栏大小: 1920x80 [0m
[32m 2025-05-27 10:40:36.910 [INFO] statusbar.cpp:1793 StatusBar() 状态栏构造完成 [0m
[32m 2025-05-27 10:40:36.922 [INFO] toolbar.cpp:37 ToolBar() 工具栏初始化完成 [0m
[32m 2025-05-27 10:40:36.922 [INFO] toolbar.cpp:180 updatePosition() 更新工具栏位置: (1820, 100)，大小: 80x1000 [0m
[32m 2025-05-27 10:40:36.930 [INFO] mainwindow.cpp:183 updateNavigationBarPosition() 更新导航栏位置: (335, 1070) [0m
[32m 2025-05-27 10:40:36.930 [INFO] mainwindow.cpp:196 updateStatusBarPosition() 更新状态栏位置: (0, 0)，宽度: 1920，高度: 80 [0m
[32m 2025-05-27 10:40:36.969 [INFO] mainwindow.cpp:207 loadStyleSheet() 样式表加载成功 [0m
[32m 2025-05-27 10:40:36.969 [INFO] mainwindow.cpp:168 setupUi() 主窗口UI设置完成 [0m
[32m 2025-05-27 10:40:36.969 [INFO] mainwindow.cpp:51 MainWindow() 使用模型路径: /home/<USER>/App/Qt/SmartScope/models/stereo_model.onnx [0m
[32m 2025-05-27 10:40:36.969 [INFO] inference_service.cpp:375 logInfo() 推理服务已经初始化 [0m
[32m 2025-05-27 10:40:36.969 [INFO] mainwindow.cpp:57 MainWindow() 推理服务初始化成功 [0m
[32m 2025-05-27 10:40:36.997 [INFO] toolbar.cpp:180 updatePosition() 更新工具栏位置: (1820, 100)，大小: 80x1000 [0m
[32m 2025-05-27 10:40:36.997 [INFO] mainwindow.cpp:183 updateNavigationBarPosition() 更新导航栏位置: (335, 1068) [0m
[32m 2025-05-27 10:40:36.997 [INFO] mainwindow.cpp:196 updateStatusBarPosition() 更新状态栏位置: (0, 0)，宽度: 1920，高度: 80 [0m
[32m 2025-05-27 10:40:36.998 [INFO] home_page.cpp:1082 showEvent() 主页显示事件开始 [0m
[32m 2025-05-27 10:40:36.998 [INFO] home_page.cpp:1151 showEvent() 主页显示事件结束 [0m
[32m 2025-05-27 10:40:37.002 [INFO] navigation_bar.cpp:266 resizeEvent() 导航栏大小变化: 1250x122 [0m
[32m 2025-05-27 10:40:37.002 [INFO] navigation_bar.cpp:236 calculateOptimalWidth() 计算导航栏最佳宽度: 1250 [0m
[32m 2025-05-27 10:40:37.002 [INFO] navigation_bar.cpp:277 adjustSizeToContent() 调整导航栏大小: 1250x122 [0m
[32m 2025-05-27 10:40:37.002 [INFO] navigation_bar.cpp:251 showEvent() 导航栏显示事件处理 [0m
[32m 2025-05-27 10:40:37.005 [INFO] statusbar.cpp:1835 resizeEvent() 状态栏大小变化: 1920x80 [0m
[32m 2025-05-27 10:40:37.005 [INFO] statusbar.cpp:1805 calculateOptimalWidth() 计算状态栏最佳宽度 (占满屏幕): 1920 [0m
[32m 2025-05-27 10:40:37.005 [INFO] statusbar.cpp:1844 adjustSizeToContent() 调整状态栏大小: 1920x80 [0m
[32m 2025-05-27 10:40:37.005 [INFO] statusbar.cpp:1820 showEvent() 状态栏显示事件处理 [0m
[32m 2025-05-27 10:40:37.039 [INFO] home_page.cpp:647 initCameras() 初始化相机... [0m
[33m 2025-05-27 10:40:37.341 [WARNING] home_page.cpp:498 findCameraDevice() 未能通过名称匹配找到相机设备，尝试根据USB总线位置识别 [0m
[32m 2025-05-27 10:40:37.341 [INFO] home_page.cpp:629 findCameraDevice() 通过USB总线位置识别左相机 位置: 1.3 路径: /dev/video1 [0m
[33m 2025-05-27 10:40:37.374 [WARNING] home_page.cpp:498 findCameraDevice() 未能通过名称匹配找到相机设备，尝试根据USB总线位置识别 [0m
[32m 2025-05-27 10:40:37.374 [INFO] home_page.cpp:632 findCameraDevice() 通过USB总线位置识别右相机 位置: 1.2.3 路径: /dev/video3 [0m
[32m 2025-05-27 10:40:37.374 [INFO] home_page.cpp:692 initCameras() 左相机设备路径: /dev/video1 [0m
[32m 2025-05-27 10:40:37.375 [INFO] home_page.cpp:693 initCameras() 右相机设备路径: /dev/video3 [0m
[32m 2025-05-27 10:40:37.375 [INFO] home_page.cpp:733 initCameras() 相机初始化完成 [0m
[32m 2025-05-27 10:40:37.375 [INFO] home_page.cpp:754 enableCameras() 主页启用相机... [0m
[32m 2025-05-27 10:40:37.375 [INFO] multi_camera_manager.cpp:812 addReference() 添加引用: HomePage 当前引用计数: 0 [0m
[32m 2025-05-27 10:40:37.375 [INFO] multi_camera_manager.cpp:826 addReference() 引用计数增加，新计数: 1 客户端: HomePage [0m
[32m 2025-05-27 10:40:37.375 [INFO] multi_camera_manager.cpp:830 addReference() 首次引用，启动相机 [0m
[32m 2025-05-27 10:40:37.375 [INFO] multi_camera_manager.cpp:227 startAll() 启动所有相机... [0m
[32m 2025-05-27 10:40:37.375 [INFO] multi_camera_manager.cpp:234 startAll() 启动相机捕获线程: /dev/video1 [0m
左相机线程启动，设备: /dev/video1
尝试连接左相机: /dev/video1
[32m 2025-05-27 10:40:37.375 [INFO] multi_camera_manager.cpp:234 startAll() 启动相机捕获线程: /dev/video3 [0m
右相机线程启动，设备: /dev/video3
尝试连接右相机: /dev/video3
[32m 2025-05-27 10:40:37.375 [INFO] multi_camera_manager.cpp:239 startAll() 启动同步线程 [0m
同步线程启动
[32m 2025-05-27 10:40:37.375 [INFO] multi_camera_manager.cpp:241 startAll() 所有相机线程已启动 [0m
[32m 2025-05-27 10:40:37.375 [INFO] home_page.cpp:777 enableCameras() 主页相机启用完成 [0m
[32m 2025-05-27 10:40:37.376 [INFO] measurement_page.cpp:345 operator()() Magnifier init: leftWidth=100, totalWidth=100, leftAreaRatio=1.000 [0m
左图像为空，放大镜不显示内容
[32m 2025-05-27 10:40:37.384 [INFO] measurement_page.cpp:361 operator()() 放大镜初始化完成 - 默认隐藏状态 [0m
[32m 2025-05-27 10:40:37.385 [INFO] temperature_controller.cpp:89 readTemperature() 开始读取温度... [0m
[32m 2025-05-27 10:40:37.385 [INFO] temperature_controller.cpp:72 startMonitoring() 温度监控已启动，间隔: 10000ms [0m
温度监控已延迟启动
[32m 2025-05-27 10:40:37.430 [INFO] temperature_controller.cpp:99 operator()() 温度读取成功: 26.2°C [0m
[32m 2025-05-27 10:40:37.455 [INFO] statusbar.cpp:1729 setCurrentPath() 当前路径已更改为: /home/<USER>/data/CAM [0m
[32m 2025-05-27 10:40:37.455 [INFO] home_page.cpp:1107 operator()() 在显示事件中连接状态栏的路径选择器 [0m
[32m 2025-05-27 10:40:37.455 [INFO] home_page.cpp:754 enableCameras() 主页启用相机... [0m
[32m 2025-05-27 10:40:37.455 [INFO] multi_camera_manager.cpp:812 addReference() 添加引用: HomePage 当前引用计数: 1 [0m
[32m 2025-05-27 10:40:37.455 [INFO] multi_camera_manager.cpp:816 addReference() 客户端已引用，不重复计数: HomePage [0m
[32m 2025-05-27 10:40:37.455 [INFO] home_page.cpp:777 enableCameras() 主页相机启用完成 [0m
[32m 2025-05-27 10:40:37.455 [INFO] toolbar.cpp:180 updatePosition() 更新工具栏位置: (1820, 100)，大小: 80x1000 [0m
[32m 2025-05-27 10:40:37.457 [INFO] toolbar.cpp:96 addButton() 创建按钮: adjustButton, 可接收鼠标事件: 1 [0m
[32m 2025-05-27 10:40:37.457 [INFO] toolbar.cpp:242 rearrangeButtons() 按钮 adjustButton 位置: (0, 20) [0m
[32m 2025-05-27 10:40:37.459 [INFO] toolbar.cpp:109 addButton() 添加按钮: adjustButton, 位置: 0 [0m
[32m 2025-05-27 10:40:37.459 [INFO] home_page.cpp:245 initToolBarButtons() 相机调节按钮已添加到工具栏 [0m
[32m 2025-05-27 10:40:37.461 [INFO] toolbar.cpp:96 addButton() 创建按钮: captureButton, 可接收鼠标事件: 1 [0m
[32m 2025-05-27 10:40:37.461 [INFO] toolbar.cpp:242 rearrangeButtons() 按钮 adjustButton 位置: (0, 20) [0m
[32m 2025-05-27 10:40:37.461 [INFO] toolbar.cpp:242 rearrangeButtons() 按钮 captureButton 位置: (0, 120) [0m
[32m 2025-05-27 10:40:37.462 [INFO] toolbar.cpp:109 addButton() 添加按钮: captureButton, 位置: 1 [0m
[32m 2025-05-27 10:40:37.462 [INFO] home_page.cpp:260 initToolBarButtons() 截图按钮已添加到工具栏 [0m
[32m 2025-05-27 10:40:37.463 [INFO] toolbar.cpp:96 addButton() 创建按钮: screenshotButton, 可接收鼠标事件: 1 [0m
[32m 2025-05-27 10:40:37.463 [INFO] toolbar.cpp:242 rearrangeButtons() 按钮 adjustButton 位置: (0, 20) [0m
[32m 2025-05-27 10:40:37.463 [INFO] toolbar.cpp:242 rearrangeButtons() 按钮 captureButton 位置: (0, 120) [0m
[32m 2025-05-27 10:40:37.463 [INFO] toolbar.cpp:242 rearrangeButtons() 按钮 screenshotButton 位置: (0, 220) [0m
[32m 2025-05-27 10:40:37.464 [INFO] toolbar.cpp:109 addButton() 添加按钮: screenshotButton, 位置: 2 [0m
[32m 2025-05-27 10:40:37.464 [INFO] home_page.cpp:284 initToolBarButtons() 屏幕截图按钮已添加到工具栏 [0m
[32m 2025-05-27 10:40:37.464 [INFO] toolbar.cpp:96 addButton() 创建按钮: ledControlButton, 可接收鼠标事件: 1 [0m
[32m 2025-05-27 10:40:37.465 [INFO] toolbar.cpp:242 rearrangeButtons() 按钮 adjustButton 位置: (0, 20) [0m
[32m 2025-05-27 10:40:37.465 [INFO] toolbar.cpp:242 rearrangeButtons() 按钮 captureButton 位置: (0, 120) [0m
[32m 2025-05-27 10:40:37.465 [INFO] toolbar.cpp:242 rearrangeButtons() 按钮 screenshotButton 位置: (0, 220) [0m
[32m 2025-05-27 10:40:37.465 [INFO] toolbar.cpp:242 rearrangeButtons() 按钮 ledControlButton 位置: (0, 320) [0m
[32m 2025-05-27 10:40:37.465 [INFO] toolbar.cpp:109 addButton() 添加按钮: ledControlButton, 位置: 3 [0m
[32m 2025-05-27 10:40:37.465 [INFO] home_page.cpp:316 initToolBarButtons() LED控制按钮已添加到工具栏 [0m
[32m 2025-05-27 10:40:37.466 [INFO] toolbar.cpp:96 addButton() 创建按钮: detectionButton, 可接收鼠标事件: 1 [0m
[32m 2025-05-27 10:40:37.466 [INFO] toolbar.cpp:242 rearrangeButtons() 按钮 adjustButton 位置: (0, 20) [0m
[32m 2025-05-27 10:40:37.466 [INFO] toolbar.cpp:242 rearrangeButtons() 按钮 captureButton 位置: (0, 120) [0m
[32m 2025-05-27 10:40:37.466 [INFO] toolbar.cpp:242 rearrangeButtons() 按钮 screenshotButton 位置: (0, 220) [0m
[32m 2025-05-27 10:40:37.466 [INFO] toolbar.cpp:242 rearrangeButtons() 按钮 ledControlButton 位置: (0, 320) [0m
[32m 2025-05-27 10:40:37.466 [INFO] toolbar.cpp:242 rearrangeButtons() 按钮 detectionButton 位置: (0, 420) [0m
[32m 2025-05-27 10:40:37.467 [INFO] toolbar.cpp:109 addButton() 添加按钮: detectionButton, 位置: 4 [0m
[32m 2025-05-27 10:40:37.467 [INFO] home_page.cpp:335 initToolBarButtons() 目标检测按钮已添加到工具栏 [0m
左相机参数: 1280x720 @ 30fps, 格式: MJPG, 缓冲区: 4
右相机参数: 1280x720 @ 30fps, 格式: MJPG, 缓冲区: 4
[32m 2025-05-27 10:40:47.286 [INFO] temperature_controller.cpp:89 readTemperature() 开始读取温度... [0m
[32m 2025-05-27 10:40:47.298 [INFO] temperature_controller.cpp:99 operator()() 温度读取成功: 27.0°C [0m
