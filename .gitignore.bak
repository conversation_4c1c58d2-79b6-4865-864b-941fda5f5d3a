# CMake generated files and directories (recursive)
CMakeCache.txt
**/CMakeFiles/
Makefile
cmake_install.cmake
*_autogen/
CMakeRuleHashes.txt
TargetDirectories.txt

# Build directories (常见的构建目录名)
build/
build-*/
Build/
build_debug/
build_release/
bin/
lib/
# SmartScopeQt 可执行文件 (如果特定于构建)
SmartScopeQt

# Compiled Object files
*.slo
*.lo
*.o
*.obj


# Compiled Static libraries
*.lai
*.la
*.a
*.lib

# Executables (generic)
*.exe
*.out
*.app

# IDE / Editor specific files
.vscode/
.idea/
*.suo
*.user
*.userprefs
*~

# OS specific
.DS_Store
.directory
.Trash-*
Thumbs.db

# Other temporary files
*.log
*.swp
*.swo

# Qt specific generated files
*.pro.user*
*.qmake.stash
*.moc
moc_*.cpp
qrc_*.cpp
ui_*.h
Makefile* # 这也会匹配根目录的 Makefile，上面已单独列出，可酌情保留或删除其一
*build-*
*.qm
.qmake.cache
.qmake.stash

# Custom directories to ignore
logs/
backup/

# Optional: Only ignore if large or generated/downloaded
# models/
# test_data/

# Ignored external projects (if managed separately)
# qt_scope/ # 如果 qt_scope 是子模块或单独管理，则忽略

# Specific build artifacts often inside CMakeFiles
*.bin
*.includecache
*.internal
*.make
link.txt
progress.marks
# Compiled Dynamic libraries\n*.so\n*.dylib\n*.dll\n\n# 例外规则 - 允许提交第三方库\n!third_party/onnxruntime/lib/*.so\n!third_party/onnxruntime/lib/*.so.*\n!third_party/hidapi/lib/*.so\n!third_party/hidapi/lib/*.so.*
