#!/usr/bin/env python3
"""
设备深度计算参数比较测试
用于验证不同设备的深度计算差异
"""

import cv2 as cv
import numpy as np
import os

def load_device_parameters(device_name=""):
    """加载设备参数"""
    try:
        # 加载左相机内参
        left_intrinsics_file = f"camera_parameters/camera0_intrinsics{device_name}.dat"
        if not os.path.exists(left_intrinsics_file):
            left_intrinsics_file = "camera_parameters/camera0_intrinsics.dat"
            
        with open(left_intrinsics_file, 'r') as f:
            lines = f.readlines()
        
        camera_matrix_left = []
        distortion_left = []
        reading_intrinsic = False
        reading_distortion = False
        
        for line in lines:
            if "intrinsic:" in line:
                reading_intrinsic = True
                reading_distortion = False
                continue
            elif "distortion:" in line:
                reading_intrinsic = False
                reading_distortion = True
                continue
                
            if reading_intrinsic:
                camera_matrix_left.append([float(x) for x in line.split()])
            elif reading_distortion:
                distortion_left = [float(x) for x in line.split()]
        
        camera_matrix_left = np.array(camera_matrix_left)
        
        # 加载外参
        rot_trans_file = f"camera_parameters/camera1_rot_trans{device_name}.dat"
        if not os.path.exists(rot_trans_file):
            rot_trans_file = "camera_parameters/camera1_rot_trans.dat"
            
        with open(rot_trans_file, 'r') as f:
            lines = f.readlines()
        
        translation_vector = []
        reading_translation = False
        
        for line in lines:
            if "T:" in line:
                reading_translation = True
                continue
                
            if reading_translation and line.strip():
                translation_vector.append(float(line.strip()))
        
        translation_vector = np.array(translation_vector)
        
        return camera_matrix_left, translation_vector
        
    except Exception as e:
        print(f"加载设备参数时出错: {e}")
        return None, None

def calculate_depth_differences():
    """计算不同设备的深度差异"""
    print("=== 设备深度计算参数比较 ===")
    
    # 加载当前设备参数
    cmtx_current, trans_current = load_device_parameters()
    if cmtx_current is None or trans_current is None:
        print("无法加载当前设备参数")
        return
    
    # 当前设备参数
    baseline_current = np.linalg.norm(trans_current)
    focal_length_current = cmtx_current[0, 0]
    
    print(f"当前设备参数:")
    print(f"  基线长度: {baseline_current:.2f} mm")
    print(f"  焦距: {focal_length_current:.2f} 像素")
    print(f"  深度缩放因子: {baseline_current * focal_length_current:.2f}")
    
    # 测试不同视差值的深度
    test_disparities = [10, 20, 50, 100]
    print(f"\n当前设备深度计算:")
    print("视差(像素) | 深度(mm) | 距离(cm)")
    print("-" * 35)
    
    for disp in test_disparities:
        depth_mm = baseline_current * focal_length_current / disp
        depth_cm = depth_mm / 10
        print(f"{disp:>8} | {depth_mm:>8.1f} | {depth_cm:>7.1f}")
    
    # 模拟其他设备的参数差异
    print(f"\n=== 参数差异影响分析 ===")
    
    # 基线长度差异的影响
    baseline_variations = [1.0, 1.5, 2.0, 3.0, 4.0]  # 不同的基线长度
    print(f"基线长度对深度的影响 (焦距固定为 {focal_length_current:.2f}):")
    print("基线(mm) | 视差10像素深度(mm) | 与当前设备差异(%)")
    print("-" * 55)
    
    for baseline in baseline_variations:
        depth = baseline * focal_length_current / 10
        diff_percent = ((depth - (baseline_current * focal_length_current / 10)) / (baseline_current * focal_length_current / 10)) * 100
        print(f"{baseline:>7.1f} | {depth:>18.1f} | {diff_percent:>15.1f}")
    
    # 焦距差异的影响
    focal_variations = [800, 850, 900, 950, 1000]  # 不同的焦距
    print(f"\n焦距对深度的影响 (基线固定为 {baseline_current:.2f}):")
    print("焦距(像素) | 视差10像素深度(mm) | 与当前设备差异(%)")
    print("-" * 55)
    
    for focal in focal_variations:
        depth = baseline_current * focal / 10
        diff_percent = ((depth - (baseline_current * focal_length_current / 10)) / (baseline_current * focal_length_current / 10)) * 100
        print(f"{focal:>10.0f} | {depth:>18.1f} | {diff_percent:>15.1f}")
    
    # 给出建议
    print(f"\n=== 建议 ===")
    print("1. 如果另一台设备的深度偏小，可能的原因:")
    print("   - 基线长度更大")
    print("   - 焦距更小")
    print("   - 标定参数不准确")
    
    print("\n2. 解决方案:")
    print("   - 重新标定另一台设备")
    print("   - 检查相机安装距离是否一致")
    print("   - 验证标定板放置是否正确")
    print("   - 确保标定过程中相机位置稳定")
    
    print("\n3. 验证方法:")
    print("   - 使用相同的标定板")
    print("   - 在相同环境下进行标定")
    print("   - 比较标定后的参数")
    print("   - 使用已知距离的物体进行测试")

def main():
    calculate_depth_differences()

if __name__ == "__main__":
    main() 