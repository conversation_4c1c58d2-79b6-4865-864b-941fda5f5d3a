以下是 **32字节 HIDRAW 通信协议** 的表格化定义，统一采用先发低字节，再发高字节的方式可直接复制使用：

---

### **协议字段表格**
rk3588 -> stm32

| 字段名         | 字节偏移 | 长度（字节） | 数据类型         | 描述                  | 示例值/规则             |
| ----------- | ---- | ------ | ------------ | ------------------- | ------------------ |
| **Header**  | 0    | 2      | `uint8_t[2]` | 协议头（固定标识）           | `0xAA 0x55`        |
| **Command** | 2    | 1      | `uint8_t`    | 指令类型                | `0x01`=读, `0x02`=写 |
| **温度**      | 3    | 2      | `int16_t`    | 温度(摄氏度*10)（写指令无效）   |                    |
| **亮度**      | 5    | 2      | `uint16_t`   | 内窥镜亮度               | 0～1279             |
| **电量**      | 7    | 2      | `uint16_t`   | 电池电量(百分比*10)        | 0～1000 (0.0%～100.0%) |
| ...         |      |        |              |                     |                    |
| **CRC16**   | 30   | 2      | `uint16_t`   | 校验和（Header到Payload） |                    |


stm32 -> rk3588

| 字段名         | 字节偏移 | 长度（字节） | 数据类型         | 描述                  | 示例值/规则                   |
| ----------- | ---- | ------ | ------------ | ------------------- | ------------------------ |
| **Header**  | 0    | 2      | `uint8_t[2]` | 协议头（固定标识）           | `0x55 0xAA`              |
| **Command** | 2    | 1      | `uint8_t`    | 指令类型                | `0x81`= 回复读, `0x82`= 回复写 |
| **温度**      | 3    | 2      | `int16_t`    | 温度(摄氏度*10)（写指令无效）   |                          |
| **亮度**      | 5    | 2      | `uint16_t`   | 内窥镜亮度               | 0～1279                   |
| **电量**      | 7    | 2      | `uint16_t`   | 电池电量(百分比*10)        | 0～1000 (0.0%～100.0%)    |
| ...         |      |        |              |                     |                          |
| **CRC16**   | 30   | 2      | `uint16_t`   | 校验和（Header到Payload） |                          |

---

### **协议规则说明**

1. **协议头**：固定 `0xAA 0x55`，用于帧同步。
    
2. **指令**：0x01=读, 0x02=写。
    
3. **CRC校验**：覆盖范围从 `Header` 到 `Payload`（共 30 字节）。
    

---

### **示例数据包（Hex格式）**

| 字段      | 值（Hex）          |
| ------- | --------------- |
| Header  | `AA 55`         |
| Command | `02`（写指令）       |
| 温度      | `00 00`         |
| 亮度      | `00 01`（2字节）    |
| CRC16   | `xx xx`（假设校验结果） |


完整数据包（32字节）：  
rk3588 tx :`AA 55 01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 16 FA`
rk3588 rx :`55 AA 81 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 C3 00`

---

如需调整字段长度或内容，可直接修改表格参数！

### **CRC16 校验代码**

```
#include <stdint.h>

// 预先生成的CRC表

static uint16_t crc16_table[256];

  

// 初始化CRC表（只需调用一次）

void init_crc16_table(void) {

	uint16_t crc;

	uint16_t i, j;

	for (i = 0; i < 256; i++) {

		crc = i << 8;

		for (j = 0; j < 8; j++) {
	
			if (crc & 0x8000) {
	
				crc = (crc << 1) ^ 0x1021;
	
			} else {
	
				crc <<= 1;
	
			}

		}

		crc16_table[i] = crc;

	}

}

  

// 使用查表法计算CRC

uint16_t crc16_ccitt_fast(const uint8_t *data, uint32_t length) {

	uint16_t crc = 0xFFFF; // 初始值

	while (length--) {

		crc = (crc << 8) ^ crc16_table[((crc >> 8) ^ *data++) & 0xFF];

	}

	return crc;

}
```