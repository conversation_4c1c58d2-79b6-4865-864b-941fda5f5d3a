# Smart Scope Qt 配置文件
# 生成时间: 2023-07-15 10:30:00

[app]
name = "Smart Scope Qt"
version = "0.1.0"
log_level = "debug"
log_file = "logs/smart_scope.log"
root_directory = "/home/<USER>/data"
default_subdirectory = "CAM"

[camera.left]
name = ["cameraL", "Web Camera 2Ks"]
parameters_path = "camera_parameters/camera0_intrinsics.dat"
rot_trans_path = "camera_parameters/camera0_rot_trans.dat"

[camera.right]
name = ["cameraR", "USB Camera"]
parameters_path = "camera_parameters/camera1_intrinsics.dat"
rot_trans_path = "camera_parameters/camera1_rot_trans.dat"

[camera.stereo]
rot_trans_path = "camera_parameters/camera0_rot_trans.dat"

[camera_control]
auto_exposure = true
exposure_time = 10000  # 微秒
gain = 1.0
auto_white_balance = true
brightness = 0
contrast = 0
saturation = 0 