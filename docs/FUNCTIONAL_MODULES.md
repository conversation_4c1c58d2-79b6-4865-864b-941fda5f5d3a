# Smart Scope Qt - 功能模块规划

## 一、基础设施模块 (Infrastructure)

### 1. 配置管理模块 (Configuration)
- **职责**：管理系统配置，提供配置读写接口
- **功能点**：
  - 配置文件读写（TOML格式）
  - 配置参数管理
  - 配置变更通知
  - 配置默认值管理
  - 配置持久化
  - 单例模式实现，全局访问点

### 2. 日志模块 (Logging)
- **职责**：提供统一的日志记录功能
- **功能点**：
  - 多级别日志（DEBUG, INFO, WARNING, ERROR, FATAL）
  - 日志格式化
  - 日志输出到多个目标（控制台、文件、网络）
  - 日志轮转
  - 性能日志

### 3. 异常处理模块 (Exception)
- **职责**：统一的异常处理机制
- **功能点**：
  - 异常类型层次结构
  - 异常信息本地化
  - 异常捕获与处理策略
  - 异常日志记录
  - 用户友好的错误提示

## 二、核心模块 (Core)

### 1. 相机模块 (Camera)
- **职责**：USB相机设备管理、图像采集
- **功能点**：
  - 相机设备发现与枚举
  - 相机连接与断开
  - 图像采集与预览
  - 相机参数控制（分辨率、帧率、曝光、增益等）
  - 双目相机同步
  - 相机状态监控
  - 热插拔支持

### 2. 相机参数调节模块 (CameraControl)
- **职责**：提供相机参数的精细调节功能
- **功能点**：
  - 曝光时间调节
  - 增益调节
  - 白平衡调节
  - 对比度/亮度/饱和度调节
  - 自动/手动对焦控制
  - 参数预设保存与加载
  - 参数调节历史记录
  - 参数效果实时预览

### 3. 立体视觉模块 (Stereo)
- **职责**：立体匹配、视差计算、深度估计
- **功能点**：
  - 图像校正
  - 立体匹配算法（BM、SGBM、神经网络）
  - 视差计算与优化
  - 视差后处理（滤波、填充）
  - 深度图生成
  - 立体匹配参数调优
  - 算法性能评估

### 4. 3D重建模块 (Reconstruction)
- **职责**：点云生成、3D模型重建
- **功能点**：
  - 点云生成
  - 点云滤波与降采样
  - 点云配准
  - 表面重建
  - 网格生成
  - 3D模型导出（PLY、OBJ等格式）
  - 点云测量工具

### 5. 推理模块 (Inference)
- **职责**：深度学习模型推理
- **功能点**：
  - 模型加载与管理
  - 推理引擎（ONNX Runtime）
  - 模型输入预处理
  - 模型输出后处理
  - 推理性能优化
  - 多模型切换
  - 硬件加速（CPU/GPU/NPU）

## 三、应用模块 (Application)

### 1. UI模块 (UI)
- **职责**：用户界面
- **功能点**：
  - 主窗口布局
  - 多页面管理
  - 控制面板
  - 状态栏
  - 菜单与工具栏
  - 快捷键支持
  - 主题与样式
  - 国际化支持

### 2. 可视化模块 (Visualization)
- **职责**：数据可视化
- **功能点**：
  - 视差图可视化
  - 深度图可视化（伪彩色）
  - 点云可视化（3D）
  - 3D模型可视化
  - 可视化参数调节（颜色映射、点大小等）
  - 截图功能
  - 视图操作（旋转、缩放、平移）

### 3. 交互模块 (Interaction)
- **职责**：用户交互
- **功能点**：
  - 鼠标交互
  - 键盘快捷键
  - 手势识别
  - 命令模式实现
  - 撤销/重做支持
  - 交互式测量工具
  - 交互式标注工具

### 4. 图片注释模块 (Annotation)
- **职责**：为图像添加注释和标记
- **功能点**：
  - 文本注释
  - 箭头标记
  - 区域高亮
  - 测量标记（距离、角度、面积）
  - 注释样式编辑
  - 注释导出与导入
  - 批量注释
  - 注释历史管理

### 5. 报告生成模块 (Reporting)
- **职责**：生成分析报告
- **功能点**：
  - 报告模板管理
  - 报告内容编辑
  - 图像与数据嵌入
  - 测量结果统计
  - 报告导出（PDF、HTML、DOCX）
  - 报告打印
  - 报告批量生成
  - 报告历史管理

### 6. 文件浏览模块 (FileBrowser)
- **职责**：管理和浏览保存的图像和视频文件
- **功能点**：
  - 文件列表视图
  - 缩略图预览
  - 文件筛选与排序
  - 文件元数据显示
  - 文件操作（重命名、删除、复制）
  - 批量处理
  - 文件导入导出
  - 文件夹组织

## 四、工具模块 (Utils)

### 1. 图像处理工具 (ImageUtils)
- **职责**：图像处理通用功能
- **功能点**：
  - 图像格式转换
  - 图像滤波
  - 图像增强
  - 图像变换（旋转、缩放、裁剪）
  - 色彩空间转换
  - 图像特征提取
  - 图像质量评估

### 2. 数学工具 (MathUtils)
- **职责**：数学计算
- **功能点**：
  - 几何计算
  - 坐标变换
  - 统计计算
  - 插值算法
  - 优化算法
  - 数值分析
  - 随机数生成

### 3. 并发工具 (ConcurrencyUtils)
- **职责**：多线程和并发
- **功能点**：
  - 线程池
  - 工作线程
  - 任务队列
  - 异步任务
  - 线程同步
  - 并行算法
  - 性能监控

### 4. 文件工具 (FileUtils)
- **职责**：文件操作
- **功能点**：
  - 文件读写
  - 目录操作
  - 文件格式检测
  - 文件压缩/解压
  - 文件加密/解密
  - 文件监控
  - 临时文件管理

## 五、数据模块 (Data)

### 1. 数据存储模块 (Storage)
- **职责**：管理应用数据的持久化
- **功能点**：
  - 图像数据存储
  - 视频数据存储
  - 点云数据存储
  - 配置数据存储
  - 用户数据存储
  - 缓存管理
  - 数据备份与恢复

### 2. 数据导入导出模块 (DataIO)
- **职责**：数据的导入与导出
- **功能点**：
  - 图像导入导出（多种格式）
  - 视频导入导出
  - 点云导入导出
  - 批量导入导出
  - 导入导出进度监控
  - 格式转换
  - 数据压缩

## 六、集成模块 (Integration)

### 1. 插件系统 (PluginSystem)
- **职责**：支持功能扩展
- **功能点**：
  - 插件加载与卸载
  - 插件依赖管理
  - 插件版本控制
  - 插件配置
  - 插件市场
  - 插件开发SDK
  - 插件安全性检查

### 2. 脚本支持 (ScriptSupport)
- **职责**：支持脚本自动化
- **功能点**：
  - 脚本引擎集成
  - 脚本API
  - 脚本编辑器
  - 脚本调试
  - 脚本录制
  - 脚本库管理
  - 脚本安全沙箱

## 模块间关系

### 数据流向
1. **相机采集流**：
   ```
   相机设备 -> 相机模块 -> 图像预处理 -> 应用程序
   ```

2. **立体处理流**：
   ```
   左右图像 -> 图像校正 -> 立体匹配 -> 视差计算 -> 视差过滤 -> 深度估计
   ```

3. **3D重建流**：
   ```
   视差图 + 相机参数 -> 点云生成 -> 点云过滤 -> 表面重建 -> 3D可视化
   ```

4. **注释与报告流**：
   ```
   图像/点云数据 -> 注释模块 -> 报告生成模块 -> 报告导出
   ```

5. **文件管理流**：
   ```
   数据采集 -> 数据存储 -> 文件浏览模块 -> 数据导入导出
   ```

### 模块依赖关系
- **基础设施模块**：被所有其他模块依赖
- **核心模块**：依赖基础设施模块，被应用模块依赖
- **应用模块**：依赖核心模块和工具模块
- **工具模块**：依赖基础设施模块，被多个模块使用
- **数据模块**：依赖基础设施模块，被应用模块和核心模块使用
- **集成模块**：依赖多个模块，提供扩展能力

## 开发优先级

### 第一阶段：基础框架
1. 基础设施模块（配置、日志、异常）
2. 相机模块（基本采集功能）
3. UI模块（主窗口和基本布局）
4. 文件工具（基本文件操作）

### 第二阶段：核心功能
1. 立体视觉模块（基本立体匹配）
2. 3D重建模块（基本点云生成）
3. 可视化模块（基本显示功能）
4. 相机参数调节模块

### 第三阶段：增强功能
1. 图片注释模块
2. 文件浏览模块
3. 报告生成模块
4. 数据存储模块

### 第四阶段：高级功能
1. 推理模块
2. 插件系统
3. 脚本支持
4. 高级可视化和交互功能 