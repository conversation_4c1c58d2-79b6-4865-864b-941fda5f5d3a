class InferenceService : public QObject
{
    Q_OBJECT

public:
    explicit InferenceService(QObject *parent = nullptr);
    ~InferenceService();

    bool initialize(const QString& modelPath);
    void submitRequest(const SmartScope::InferenceRequest& request);

signals:
    void inferenceCompleted(const SmartScope::InferenceResult& result);

private:
    cv::Mat preprocessImage(const cv::Mat& image);
    Ort::Value createInputTensor(const cv::Mat& image, const std::vector<int64_t>& shape);
    
    std::unique_ptr<Ort::Session> m_session;
    std::vector<const char*> m_input_node_names;
    std::vector<const char*> m_output_node_names;
    bool m_inferenceInitialized;
}; 