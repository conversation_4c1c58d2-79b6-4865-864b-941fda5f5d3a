#pragma once

#include <string>
#include <vector>
#include <memory>
#include <map>
#include <opencv2/opencv.hpp>
// 移除 ONNX Runtime 依赖，添加 LightStereo 依赖
// #include <onnxruntime_cxx_api.h>
// 添加 LightStereo 推理引擎
#include "lightstereo_inference.hpp"
// 添加PCL相关头文件
#include <pcl/io/pcd_io.h>
#include <pcl/io/ply_io.h>
#include <pcl/point_types.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/filters/statistical_outlier_removal.h>
#include <pcl/filters/radius_outlier_removal.h>
#include <pcl/sample_consensus/method_types.h>
#include <pcl/sample_consensus/model_types.h>
#include <pcl/segmentation/sac_segmentation.h>
#include <pcl/filters/extract_indices.h>
#include <pcl/filters/project_inliers.h>
#include <pcl/common/transforms.h>
#include <pcl/console/time.h>

// 点云过滤参数结构体
struct PointCloudFilterParams {
    float voxel_size = 0.02f;       // 体素大小，增大以减少点数
    float min_depth = 0.8f;         // 最小深度，略微增加以过滤近处噪点
    float max_depth = 20.0f;        // 最大深度，减小以过滤远处不准确的点
    int mean_k = 50;                // 统计滤波邻域点数，增加点数以提高过滤效果
    float std_dev_mul = 0.8f;       // 统计滤波标准差倍数，减小以过滤更多离群点
    float radius = 0.08f;           // 半径滤波搜索半径，增大以过滤孤立点
    int min_neighbors = 15;         // 半径滤波最小邻居数量，增加以过滤更多稀疏点
};

// 前向声明平面拟合结果结构体和平面优化函数
struct PlaneResult;
pcl::PointCloud<pcl::PointXYZRGB>::Ptr optimizePointCloud(
    const pcl::PointCloud<pcl::PointXYZRGB>::Ptr& input_cloud,
    double distance_threshold,
    bool project_to_plane,
    int max_iterations);

class StereoInference {
public:
    // 性能模式枚举
    enum PerformanceMode {
        HIGH_QUALITY,   // 高质量模式
        BALANCED,       // 平衡模式
        FAST,           // 快速模式
        ULTRA_FAST      // 超快模式
    };
    
    // 固定的输入图像尺寸
    static constexpr int INPUT_WIDTH = 720;
    static constexpr int INPUT_HEIGHT = 1280;
    
    // 固定的裁剪和缩放尺寸
    static constexpr int CROP_WIDTH = 720;
    static constexpr int CROP_HEIGHT = 736;
    static constexpr int MODEL_WIDTH = 736;
    static constexpr int MODEL_HEIGHT = 736;
    
    // 固定的通道数
    static constexpr int CHANNELS = 3;
    
    // 固定的内存对齐值
    static constexpr int DIVIS_BY = 32;
    
    // 固定的预处理参数
    static constexpr float MEAN[3] = {0.485f, 0.456f, 0.406f};
    static constexpr float STD[3] = {0.229f, 0.224f, 0.225f};

    StereoInference(const std::string& model_path = "");
    ~StereoInference();

    // 性能模式设置
    void setPerformanceMode(PerformanceMode mode);
    PerformanceMode getPerformanceMode() const { return perf_mode_; }

    // 推理函数
    cv::Mat inference(const cv::Mat& left_img, const cv::Mat& right_img);

    // 保存结果
    void saveDisparity(const cv::Mat& disparity, const std::string& filename);
    void savePointCloud(const cv::Mat& disparity, const cv::Mat& color, 
                       const std::string& filename,
                       float baseline = 0.23f, float focal_length = -1.0f);
                       
    // 比较函数
    void compareWithReference(const cv::Mat& disparity, const std::string& ref_npy,
                            const std::string& output_dir);
    
    // 辅助函数
    std::pair<cv::Mat, cv::Mat> pad_images(const cv::Mat& img1, const cv::Mat& img2);
    cv::Mat unpad_disparity(const cv::Mat& disparity, const cv::Size& original_size);
    cv::Mat crop_center_height(const cv::Mat& img, int target_height);
    std::vector<float> preprocess(const cv::Mat& img);
    
    // 新增预处理函数
    cv::Mat preprocessCropFixed(const cv::Mat& img);
    
    // 点云过滤和优化函数
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr voxelGridFilter(
        const pcl::PointCloud<pcl::PointXYZRGB>::Ptr& cloud,
        float leaf_size);
        
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr statisticalOutlierRemoval(
        const pcl::PointCloud<pcl::PointXYZRGB>::Ptr& cloud,
        int mean_k,
        float std_dev_mul);
        
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr radiusOutlierRemoval(
        const pcl::PointCloud<pcl::PointXYZRGB>::Ptr& cloud,
        float radius,
        int min_neighbors);
        
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr depthRangeFilter(
        const pcl::PointCloud<pcl::PointXYZRGB>::Ptr& cloud,
        float min_depth,
        float max_depth);
        
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr filterPointCloud(
        const pcl::PointCloud<pcl::PointXYZRGB>::Ptr& cloud,
        const PointCloudFilterParams& params);
        
    bool filterPointCloudFile(
        const std::string& input_file,
        const std::string& output_file,
        const PointCloudFilterParams& params);
        
    // 点云优化函数
    bool saveOptimizedPointCloud(
        const std::string& input_file,
        const std::string& output_file,
        double distance_threshold = 0.02,
        bool project_to_plane = false);

private:
    // 预处理函数
    void preprocess_small(const cv::Mat& img, std::vector<float>& output);
    
    // 后处理函数
    cv::Mat postprocess(const std::vector<float>& output, 
                       const std::vector<int64_t>& output_shape);
                       
    // 加载NPY文件
    cv::Mat loadNpy(const std::string& filename);
    
    // 更新输入尺寸
    void updateInputShape();
    
    // 优化的辅助函数
    void optimizeMemoryLayout();

    // LightStereo 适配函数
    bool initializeLightStereo();
    std::string getModelPathForMode(PerformanceMode mode);
    cv::Mat adaptLightStereoOutput(const cv::Mat& lightstereo_disparity,
                                   const cv::Size& target_size,
                                   const lightstereo_inference::ScaleInfo& scale_info);

    // LightStereo 推理引擎
    std::unique_ptr<lightstereo_inference::LightStereoInference> lightstereo_engine_;
    std::string model_path_;

    // 性能模式到模型的映射
    std::map<PerformanceMode, std::string> model_map_;
    
    // 预分配的输入缓冲区
    std::vector<float> input_buffer1_;
    std::vector<float> input_buffer2_;
    
    // 裁剪信息
    cv::Rect crop_roi_;
    
    // 性能模式
    PerformanceMode perf_mode_ = BALANCED;
    
    // 模型尺寸
    int width_ = MODEL_WIDTH;
    int height_ = MODEL_HEIGHT;
    int channels_ = CHANNELS;
    int divis_by_ = DIVIS_BY;
    
    // 默认裁剪尺寸
    int crop_width_ = CROP_WIDTH;
    int crop_height_ = CROP_HEIGHT;
}; 