#pragma once

#include <string>
#include <vector>
#include <memory>
#include <map>
#include <opencv2/opencv.hpp>
// 移除 ONNX Runtime 依赖，添加 LightStereo 依赖
// #include <onnxruntime_cxx_api.h>
// 添加 LightStereo 推理引擎
#include "lightstereo_inference.hpp"
// 移除 PCL 依赖以避免编译问题，使用简化的点云处理
// #include <pcl/io/pcd_io.h>
// #include <pcl/io/ply_io.h>
// ... 其他 PCL 头文件已移除

// 简化的点云过滤参数结构体（移除PCL依赖）
struct PointCloudFilterParams {
    float voxel_size = 0.02f;       // 体素大小
    float min_depth = 0.8f;         // 最小深度
    float max_depth = 20.0f;        // 最大深度
    int mean_k = 50;                // 统计滤波邻域点数
    float std_dev_mul = 0.8f;       // 统计滤波标准差倍数
    float radius = 0.08f;           // 半径滤波搜索半径
    int min_neighbors = 15;         // 半径滤波最小邻居数量
};

// 简化的点云结构体（替代PCL）
struct SimplePoint3D {
    float x, y, z;
    uint8_t r, g, b;
};

class StereoInference {
public:
    // 性能模式枚举
    enum PerformanceMode {
        HIGH_QUALITY,   // 高质量模式
        BALANCED,       // 平衡模式
        FAST,           // 快速模式
        ULTRA_FAST      // 超快模式
    };
    
    // 固定的输入图像尺寸
    static constexpr int INPUT_WIDTH = 720;
    static constexpr int INPUT_HEIGHT = 1280;
    
    // 固定的裁剪和缩放尺寸
    static constexpr int CROP_WIDTH = 720;
    static constexpr int CROP_HEIGHT = 736;
    static constexpr int MODEL_WIDTH = 736;
    static constexpr int MODEL_HEIGHT = 736;
    
    // 固定的通道数
    static constexpr int CHANNELS = 3;
    
    // 固定的内存对齐值
    static constexpr int DIVIS_BY = 32;
    
    // 固定的预处理参数
    static constexpr float MEAN[3] = {0.485f, 0.456f, 0.406f};
    static constexpr float STD[3] = {0.229f, 0.224f, 0.225f};

    StereoInference(const std::string& model_path = "");
    ~StereoInference();

    // 性能模式设置
    void setPerformanceMode(PerformanceMode mode);
    PerformanceMode getPerformanceMode() const { return perf_mode_; }

    // 推理函数
    cv::Mat inference(const cv::Mat& left_img, const cv::Mat& right_img);

    // 保存结果
    void saveDisparity(const cv::Mat& disparity, const std::string& filename);
    void savePointCloud(const cv::Mat& disparity, const cv::Mat& color, 
                       const std::string& filename,
                       float baseline = 0.23f, float focal_length = -1.0f);
                       
    // 比较函数
    void compareWithReference(const cv::Mat& disparity, const std::string& ref_npy,
                            const std::string& output_dir);
    
    // 辅助函数
    std::pair<cv::Mat, cv::Mat> pad_images(const cv::Mat& img1, const cv::Mat& img2);
    cv::Mat unpad_disparity(const cv::Mat& disparity, const cv::Size& original_size);
    cv::Mat crop_center_height(const cv::Mat& img, int target_height);
    std::vector<float> preprocess(const cv::Mat& img);
    
    // 新增预处理函数
    cv::Mat preprocessCropFixed(const cv::Mat& img);
    
    // 简化的点云处理函数（移除PCL依赖）
    std::vector<SimplePoint3D> generatePointCloud(
        const cv::Mat& disparity,
        const cv::Mat& color,
        float baseline = 0.23f,
        float focal_length = -1.0f);

    bool saveSimplePointCloud(
        const std::vector<SimplePoint3D>& points,
        const std::string& filename);

private:
    // 预处理函数
    void preprocess_small(const cv::Mat& img, std::vector<float>& output);
    
    // 后处理函数
    cv::Mat postprocess(const std::vector<float>& output, 
                       const std::vector<int64_t>& output_shape);
                       
    // 加载NPY文件
    cv::Mat loadNpy(const std::string& filename);
    
    // 更新输入尺寸
    void updateInputShape();
    
    // 优化的辅助函数
    void optimizeMemoryLayout();

    // LightStereo 适配函数
    bool initializeLightStereo();
    std::string getModelPathForMode(PerformanceMode mode);
    cv::Mat adaptLightStereoOutput(const cv::Mat& lightstereo_disparity,
                                   const cv::Size& target_size,
                                   const lightstereo_inference::ScaleInfo& scale_info);

    // LightStereo 推理引擎
    std::unique_ptr<lightstereo_inference::LightStereoInference> lightstereo_engine_;
    std::string model_path_;

    // 性能模式到模型的映射
    std::map<PerformanceMode, std::string> model_map_;
    
    // 预分配的输入缓冲区
    std::vector<float> input_buffer1_;
    std::vector<float> input_buffer2_;
    
    // 裁剪信息
    cv::Rect crop_roi_;
    
    // 性能模式
    PerformanceMode perf_mode_ = BALANCED;
    
    // 模型尺寸
    int width_ = MODEL_WIDTH;
    int height_ = MODEL_HEIGHT;
    int channels_ = CHANNELS;
    int divis_by_ = DIVIS_BY;
    
    // 默认裁剪尺寸
    int crop_width_ = CROP_WIDTH;
    int crop_height_ = CROP_HEIGHT;
}; 