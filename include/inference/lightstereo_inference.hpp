#pragma once

#include <opencv2/opencv.hpp>
#include <string>
#include <memory>

namespace lightstereo_inference {

/**
 * @brief 缩放信息结构体
 */
struct ScaleInfo {
    cv::Size original_size;      // 原始图像尺寸
    cv::Size model_input_size;   // 模型输入尺寸
    float scale_factor;          // 缩放因子 (original / model_input)
    cv::Size actual_input_size;  // 实际输入尺寸（考虑padding）
    
    ScaleInfo() : scale_factor(1.0f) {}
};

/**
 * @brief 深度计算参数
 */
struct DepthParams {
    float focal_length;    // 焦距 (像素单位)
    float baseline;        // 基线距离 (米)
    
    DepthParams(float f = 0.0f, float b = 0.0f) : focal_length(f), baseline(b) {}
};

class LightStereoInference {
public:
    /**
     * @brief 构造函数
     * @param model_path RKNN模型文件路径
     */
    explicit LightStereoInference(const std::string& model_path);
    
    /**
     * @brief 析构函数
     */
    ~LightStereoInference();

    /**
     * @brief 初始化推理引擎
     * @return 成功返回true，失败返回false
     */
    bool Initialize();

    /**
     * @brief 执行立体视觉推理
     * @param left_image 左图像
     * @param right_image 右图像
     * @param disparity_map 输出的视差图
     * @return 成功返回true，失败返回false
     */
    bool Inference(const cv::Mat& left_image, 
                   const cv::Mat& right_image, 
                   cv::Mat& disparity_map);

    /**
     * @brief 执行立体视觉推理并获取缩放信息
     * @param left_image 左图像
     * @param right_image 右图像
     * @param disparity_map 输出的视差图
     * @param scale_info 输出的缩放信息
     * @return 成功返回true，失败返回false
     */
    bool InferenceWithScaleInfo(const cv::Mat& left_image, 
                                const cv::Mat& right_image, 
                                cv::Mat& disparity_map,
                                ScaleInfo& scale_info);

    /**
     * @brief 执行立体视觉推理（从文件路径）
     * @param left_image_path 左图像文件路径
     * @param right_image_path 右图像文件路径
     * @param output_path 输出视差图路径
     * @return 成功返回true，失败返回false
     */
    bool InferenceFromFiles(const std::string& left_image_path,
                           const std::string& right_image_path,
                           const std::string& output_path);

    /**
     * @brief 获取模型输入尺寸
     * @return 输入尺寸 (width, height)
     */
    std::pair<int, int> GetInputSize() const { return {960, 576}; }

private:
    class Impl;
    std::unique_ptr<Impl> pImpl;
};

/**
 * @brief 将视差图转换为彩色图像用于可视化
 * @param disparity_map 输入视差图
 * @param colored_disparity 输出彩色视差图
 * @param max_disparity 最大视差值，用于归一化
 */
void VisualizeDisparity(const cv::Mat& disparity_map, 
                       cv::Mat& colored_disparity, 
                       float max_disparity = 192.0f);

/**
 * @brief 将视差图转换为深度图
 * @param disparity_map 输入视差图
 * @param depth_map 输出深度图 (米)
 * @param depth_params 深度计算参数
 * @param scale_info 缩放信息（用于校正视差值）
 */
void DisparityToDepth(const cv::Mat& disparity_map,
                     cv::Mat& depth_map,
                     const DepthParams& depth_params,
                     const ScaleInfo& scale_info);

/**
 * @brief 校正缩放后的视差图
 * @param disparity_map 输入视差图
 * @param corrected_disparity 输出校正后的视差图
 * @param scale_info 缩放信息
 */
void CorrectDisparityScale(const cv::Mat& disparity_map,
                          cv::Mat& corrected_disparity,
                          const ScaleInfo& scale_info);

} // namespace lightstereo_inference
