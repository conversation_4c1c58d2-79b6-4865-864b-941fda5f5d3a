/**
 * @file multi_camera_manager.h
 * @brief 多相机异步读流管理器
 */

#ifndef MULTI_CAMERA_MANAGER_H
#define MULTI_CAMERA_MANAGER_H

#include <opencv2/opencv.hpp>
#include <string>
#include <thread>
#include <atomic>
#include <mutex>
#include <deque>
#include <condition_variable>
#include <functional>
#include <map>
#include <memory>
#include <vector>
#include <QMap>
#include <QVariant>

namespace SmartScope {
namespace Core {
namespace CameraUtils {

/**
 * @brief 带时间戳的帧结构
 */
struct TimestampedFrame {
    cv::Mat frame;       // 图像数据
    int64_t timestamp;   // 时间戳（毫秒）
    
    // 构造函数 - 使用移动语义避免深拷贝
    TimestampedFrame(cv::Mat&& f, int64_t ts) : frame(std::move(f)), timestamp(ts) {}
    
    // 拷贝构造函数 - 使用引用共享而非深拷贝
    TimestampedFrame(const TimestampedFrame& other) : frame(other.frame), timestamp(other.timestamp) {}
};

/**
 * @brief 帧缓冲区结构
 */
class FrameBuffer {
public:
    /**
     * @brief 构造函数
     * @param size 缓冲区大小
     * @param drop 是否在缓冲区满时丢弃旧帧
     */
    FrameBuffer(size_t size = 1, bool drop = true);
    
    /**
     * @brief 添加帧到缓冲区
     * @param frame 帧数据
     * @param timestamp 时间戳
     * @return 是否成功添加
     */
    bool push(cv::Mat& frame, int64_t timestamp);
    
    /**
     * @brief 获取最新帧，不等待
     * @param frame 输出帧
     * @param timestamp 输出时间戳
     * @return 是否成功获取
     */
    bool getLatestFrame(cv::Mat& frame, int64_t& timestamp);
    
    /**
     * @brief 获取帧，可设置超时
     * @param frame 输出帧
     * @param timestamp 输出时间戳
     * @param timeoutMs 超时时间（毫秒）
     * @return 是否成功获取
     */
    bool pop(cv::Mat& frame, int64_t& timestamp, int timeoutMs = 1);
    
    /**
     * @brief 查找最接近给定时间戳的帧
     * @param frame 输出帧
     * @param targetTimestamp 目标时间戳
     * @param actualTimestamp 实际获取的帧的时间戳
     * @param maxDiffMs 最大允许时间差（毫秒）
     * @return 是否成功获取
     */
    bool findClosestFrame(cv::Mat& frame, int64_t targetTimestamp, int64_t& actualTimestamp, int maxDiffMs = 50);
    
    /**
     * @brief 清空缓冲区
     */
    void clear();
    
    /**
     * @brief 获取缓冲区大小
     * @return 缓冲区中的帧数量
     */
    size_t size();
    
    /**
     * @brief 获取最新帧的时间戳
     * @param timestamp 输出时间戳
     * @return 是否成功获取
     */
    bool getLatestTimestamp(int64_t& timestamp);

private:
    std::deque<TimestampedFrame> frames_;  // 帧队列
    std::mutex mutex_;                     // 互斥锁
    std::condition_variable cond_;         // 条件变量
    size_t maxSize_;                       // 最大缓冲区大小
    bool dropFrames_;                      // 是否丢弃旧帧
};

/**
 * @brief 相机配置结构
 */
struct CameraConfig {
    int width = 640;             // 宽度
    int height = 480;            // 高度
    int fps = 30;                // 帧率
    int bufferSize = 1;          // 相机内部缓冲区大小
    int fourcc = cv::VideoWriter::fourcc('M', 'J', 'P', 'G'); // 编码格式
    bool convertToRGB = true;    // 是否转换为RGB
    
    // 构造函数
    CameraConfig() = default;
    
    // 带参数的构造函数
    CameraConfig(int w, int h, int f, int b = 1, int cc = cv::VideoWriter::fourcc('M', 'J', 'P', 'G'), bool rgb = true)
        : width(w), height(h), fps(f), bufferSize(b), fourcc(cc), convertToRGB(rgb) {}
};

/**
 * @brief 相机信息结构
 */
struct CameraInfo {
    std::string id;              // 相机ID
    std::string name;            // 相机名称
    int frameCount = 0;          // 帧计数
    float fps = 0.0f;            // 当前帧率
    CameraConfig config;         // 相机配置
    
    // 构造函数
    CameraInfo() = default;
    
    // 带参数的构造函数
    CameraInfo(const std::string& i, const std::string& n, const CameraConfig& c = CameraConfig())
        : id(i), name(n), config(c) {}
};

/**
 * @brief 同步模式枚举
 */
enum class SyncMode {
    SYNC,       // 严格同步模式
    LOW_LATENCY, // 低延迟模式
    NO_SYNC     // 完全不同步模式
};

/**
 * @brief 多相机管理器类 - 单例模式
 */
class MultiCameraManager {
public:
    /**
     * @brief 获取单例实例
     * @return 相机管理器单例引用
     */
    static MultiCameraManager& instance();
    
    /**
     * @brief 析构函数
     */
    ~MultiCameraManager();
    
    /**
     * @brief 添加相机
     * @param cameraId 相机ID
     * @param cameraName 相机名称
     * @param config 相机配置
     * @return 是否成功添加
     */
    bool addCamera(const std::string& cameraId, const std::string& cameraName, const CameraConfig& config = CameraConfig());
    
    /**
     * @brief 移除相机
     * @param cameraId 相机ID
     * @return 是否成功移除
     */
    bool removeCamera(const std::string& cameraId);
    
    /**
     * @brief 启动所有相机
     * @return 是否成功启动
     */
    bool startAll();
    
    /**
     * @brief 停止所有相机
     */
    void stopAll();
    
    /**
     * @brief 获取相机信息
     * @param cameraId 相机ID
     * @return 相机信息
     */
    CameraInfo getCameraInfo(const std::string& cameraId);
    
    /**
     * @brief 获取所有相机信息
     * @return 相机信息列表
     */
    std::vector<CameraInfo> getAllCameraInfo();
    
    /**
     * @brief 获取最新帧
     * @param cameraId 相机ID
     * @param frame 输出帧
     * @param timestamp 输出时间戳
     * @return 是否成功获取
     */
    bool getLatestFrame(const std::string& cameraId, cv::Mat& frame, int64_t& timestamp);
    
    /**
     * @brief 获取同步帧
     * @param frames 输出帧映射（相机ID -> 帧）
     * @param timestamps 输出时间戳映射（相机ID -> 时间戳）
     * @param syncThresholdMs 同步阈值（毫秒）
     * @param mode 同步模式
     * @return 是否成功获取
     */
    bool getSyncFrames(std::map<std::string, cv::Mat>& frames, 
                       std::map<std::string, int64_t>& timestamps,
                       int syncThresholdMs = 50,
                       SyncMode mode = SyncMode::LOW_LATENCY);
    
    /**
     * @brief 设置帧回调函数
     * @param cameraId 相机ID
     * @param callback 回调函数
     */
    void setFrameCallback(const std::string& cameraId, 
                          std::function<void(const cv::Mat&, int64_t)> callback);
    
    /**
     * @brief 设置同步帧回调函数
     * @param callback 回调函数
     */
    void setSyncFrameCallback(
        std::function<void(const std::map<std::string, cv::Mat>&, 
                          const std::map<std::string, int64_t>&)> callback);
    
    /**
     * @brief 是否正在运行
     * @return 是否正在运行
     */
    bool isRunning() const;
    
    /**
     * @brief 获取同步帧计数
     * @return 同步帧计数
     */
    int getSyncFrameCount() const;
    
    /**
     * @brief 获取丢失同步计数
     * @return 丢失同步计数
     */
    int getMissedSyncCount() const;
    
    /**
     * @brief 设置同步模式
     * @param mode 同步模式
     */
    void setSyncMode(SyncMode mode);
    
    /**
     * @brief 获取同步模式
     * @return 同步模式
     */
    SyncMode getSyncMode() const;

    /**
     * @brief 增加相机引用计数
     * @param clientId 客户端ID
     * @return 当前引用计数
     */
    int addReference(const std::string& clientId);
    
    /**
     * @brief 减少相机引用计数
     * @param clientId 客户端ID
     * @return 当前引用计数
     */
    int removeReference(const std::string& clientId);
    
    /**
     * @brief 获取当前引用计数
     * @return 当前引用计数
     */
    int getReferenceCount() const;

    /**
     * @brief 设置相机参数
     * @param cameraId 相机ID
     * @param parameters 参数映射表
     * @return 是否设置成功
     */
    bool setCameraParameters(const QString& cameraId, const QMap<QString, QVariant>& parameters);

private:
    /**
     * @brief 构造函数 - 私有，防止外部创建实例
     */
    MultiCameraManager();
    
    // 禁止拷贝构造和赋值
    MultiCameraManager(const MultiCameraManager&) = delete;
    MultiCameraManager& operator=(const MultiCameraManager&) = delete;
    
    // 相机捕获线程函数
    void captureThread(const std::string& cameraId);
    
    // 同步线程函数
    void syncThread();
    
    // 成员变量
    std::map<std::string, CameraInfo> cameras_;                  // 相机信息映射
    std::map<std::string, std::unique_ptr<FrameBuffer>> buffers_; // 帧缓冲区映射
    std::map<std::string, std::thread> captureThreads_;          // 捕获线程映射
    std::map<std::string, std::function<void(const cv::Mat&, int64_t)>> frameCallbacks_; // 帧回调函数映射
    
    std::function<void(const std::map<std::string, cv::Mat>&, 
                      const std::map<std::string, int64_t>&)> syncFrameCallback_; // 同步帧回调函数
    
    std::thread syncThread_;                // 同步线程
    std::atomic<bool> running_;             // 运行标志
    std::atomic<int> syncFrameCount_;       // 同步帧计数
    std::atomic<int> missedSyncCount_;      // 丢失同步计数
    SyncMode syncMode_;                     // 同步模式
    
    std::mutex mutex_;                      // 互斥锁
    std::condition_variable syncCond_;      // 同步条件变量
    
    // 单例实例
    static std::unique_ptr<MultiCameraManager> s_instance;
    
    // 引用计数相关
    std::map<std::string, bool> clientReferences_;  // 客户端引用映射
    std::atomic<int> referenceCount_;               // 引用计数
    std::mutex referenceMutex_;                     // 引用计数互斥锁
};

} // namespace CameraUtils
} // namespace Core
} // namespace SmartScope

#endif // MULTI_CAMERA_MANAGER_H 