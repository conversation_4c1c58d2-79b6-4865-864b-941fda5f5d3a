#ifndef SMART_SCOPE_CAMERA_H
#define SMART_SCOPE_CAMERA_H

#include <QObject>
#include <QImage>
#include <QString>
#include <QSize>
#include <QMutex>
#include <QElapsedTimer>

namespace SmartScope {
namespace Core {

/**
 * @brief 相机接口类
 * 
 * 定义相机设备的通用接口，支持打开/关闭相机、获取图像等操作
 */
class Camera : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 相机类型枚举
     */
    enum class Type {
        USB,        ///< USB相机
        NETWORK,    ///< 网络相机
        FILE,       ///< 文件模拟相机
        VIRTUAL     ///< 虚拟相机
    };

    /**
     * @brief 相机参数枚举
     */
    enum class Property {
        BRIGHTNESS,     ///< 亮度
        CONTRAST,       ///< 对比度
        SATURATION,     ///< 饱和度
        HUE,            ///< 色调
        GAIN,           ///< 增益
        EXPOSURE,       ///< 曝光
        FOCUS,          ///< 焦距
        WHITE_BALANCE,  ///< 白平衡
        FRAME_RATE,     ///< 帧率
        RESOLUTION      ///< 分辨率
    };

    /**
     * @brief 构造函数
     * @param deviceId 设备ID
     * @param parent 父对象
     */
    explicit Camera(const QString& deviceId, QObject* parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    virtual ~Camera();

    /**
     * @brief 获取相机类型
     * @return 相机类型
     */
    virtual Type getType() const = 0;

    /**
     * @brief 获取相机设备ID
     * @return 设备ID
     */
    QString getDeviceId() const;

    /**
     * @brief 获取相机名称
     * @return 相机名称
     */
    virtual QString getName() const = 0;

    /**
     * @brief 打开相机
     * @return 是否成功
     */
    virtual bool open() = 0;

    /**
     * @brief 关闭相机
     */
    virtual void close() = 0;

    /**
     * @brief 检查相机是否已打开
     * @return 是否已打开
     */
    virtual bool isOpened() const = 0;

    /**
     * @brief 获取一帧图像
     * @param image 输出图像
     * @return 是否成功
     */
    virtual bool getFrame(QImage& image) = 0;

    /**
     * @brief 设置相机参数
     * @param property 参数类型
     * @param value 参数值
     * @return 是否成功
     */
    virtual bool setProperty(Property property, double value) = 0;

    /**
     * @brief 获取相机参数
     * @param property 参数类型
     * @return 参数值
     */
    virtual double getProperty(Property property) const = 0;

    /**
     * @brief 获取相机支持的分辨率列表
     * @return 分辨率列表
     */
    virtual QList<QSize> getSupportedResolutions() const = 0;

    /**
     * @brief 设置分辨率
     * @param width 宽度
     * @param height 高度
     * @return 是否成功
     */
    virtual bool setResolution(int width, int height) = 0;

    /**
     * @brief 获取当前分辨率
     * @return 当前分辨率
     */
    virtual QSize getResolution() const = 0;

    /**
     * @brief 获取当前帧率
     * @return 当前帧率
     */
    virtual double getFrameRate() const = 0;

signals:
    /**
     * @brief 新帧信号
     * @param frame 新帧图像
     */
    void newFrame(const QImage& frame);

    /**
     * @brief 错误信号
     * @param message 错误信息
     */
    void error(const QString& message);

protected:
    QString m_deviceId;     ///< 设备ID
    QMutex m_mutex;         ///< 互斥锁
    bool m_isOpened;        ///< 是否已打开
    QSize m_resolution;     ///< 当前分辨率
    double m_frameRate;     ///< 当前帧率
    QElapsedTimer m_fpsTimer;  ///< 帧率计时器
    int m_frameCount;       ///< 帧计数
};

} // namespace Core
} // namespace SmartScope

#endif // SMART_SCOPE_CAMERA_H 