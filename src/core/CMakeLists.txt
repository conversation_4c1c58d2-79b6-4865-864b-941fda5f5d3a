set(CORE_SOURCES
    camera/camera.cpp
    camera/multi_camera_manager.cpp
    # 添加core模块的其他源文件...
)

set(CORE_HEADERS
    ${CMAKE_SOURCE_DIR}/include/core/camera/camera.h
    ${CMAKE_SOURCE_DIR}/include/core/camera/multi_camera_manager.h
    # 添加core模块的其他头文件...
)

add_library(core STATIC ${CORE_SOURCES} ${CORE_HEADERS})

# 添加子模块 camera_utils - 目录不存在，移除
# add_subdirectory(camera_utils)

target_link_libraries(core
    PUBLIC
    Qt${QT_VERSION_MAJOR}::Core
    Qt${QT_VERSION_MAJOR}::Gui
    ${OpenCV_LIBS}
    infrastructure
    # camera_utils # 移除 camera_utils 链接
)

target_include_directories(core
    PUBLIC
    ${CMAKE_SOURCE_DIR}/include
    ${CMAKE_SOURCE_DIR}/src
    ${OpenCV_INCLUDE_DIRS} # 确保OpenCV头文件路径被包含
) 