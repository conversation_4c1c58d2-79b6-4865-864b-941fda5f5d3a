/**
 * @file multi_camera_manager.cpp
 * @brief 多相机异步读流管理器实现
 */

#include "core/camera/multi_camera_manager.h"
#include <chrono>
#include <iostream>
#include <algorithm>
#include <QDebug>
#include "infrastructure/logging/logger.h"

namespace SmartScope {
namespace Core {
namespace CameraUtils {

// 初始化静态单例指针
std::unique_ptr<MultiCameraManager> MultiCameraManager::s_instance = nullptr;

// 获取单例实例
MultiCameraManager& MultiCameraManager::instance() {
    if (!s_instance) {
        s_instance = std::unique_ptr<MultiCameraManager>(new MultiCameraManager());
    }
    return *s_instance;
}

// FrameBuffer 实现
FrameBuffer::FrameBuffer(size_t size, bool drop) 
    : maxSize_(size), dropFrames_(drop) {
}

bool FrameBuffer::push(cv::Mat& frame, int64_t timestamp) {
    std::unique_lock<std::mutex> lock(mutex_);
    
    // 如果队列已满且设置为丢弃旧帧
    if (frames_.size() >= maxSize_) {
        if (dropFrames_) {
            // 丢弃最旧的帧
            frames_.pop_front();
        } else {
            // 不丢弃帧，返回失败
            return false;
        }
    }
    
    // 添加新帧 - 使用移动语义避免深拷贝
    frames_.emplace_back(std::move(frame), timestamp);
    
    // 通知等待的线程
    cond_.notify_all();
    return true;
}

bool FrameBuffer::getLatestFrame(cv::Mat& frame, int64_t& timestamp) {
    std::unique_lock<std::mutex> lock(mutex_);
    
    if (frames_.empty()) {
        return false;
    }
    
    // 直接获取最新的帧，但不清空缓冲区
    frame = frames_.back().frame.clone(); // 使用clone避免引用问题
    timestamp = frames_.back().timestamp;
    
    // 保留最新帧，只清除旧帧
    if (frames_.size() > 1) {
        frames_.erase(frames_.begin(), frames_.end() - 1);
    }
    
    return true;
}

bool FrameBuffer::pop(cv::Mat& frame, int64_t& timestamp, int timeoutMs) {
    std::unique_lock<std::mutex> lock(mutex_);
    
    // 如果队列为空，几乎不等待
    if (frames_.empty()) {
        if (timeoutMs > 0) {
            auto status = cond_.wait_for(lock, std::chrono::milliseconds(timeoutMs));
        }
        if (frames_.empty()) {
            return false;
        }
    }
    
    // 取出最旧的帧 - 使用引用共享而非深拷贝
    frame = frames_.front().frame;
    timestamp = frames_.front().timestamp;
    frames_.pop_front();
    return true;
}

bool FrameBuffer::findClosestFrame(cv::Mat& frame, int64_t targetTimestamp, int64_t& actualTimestamp, int maxDiffMs) {
    std::unique_lock<std::mutex> lock(mutex_);
    
    if (frames_.empty()) {
        return false;
    }
    
    // 如果只有一帧，直接返回
    if (frames_.size() == 1) {
        frame = frames_.front().frame;
        actualTimestamp = frames_.front().timestamp;
        frames_.pop_front();
        return true;
    }
    
    // 查找最接近目标时间戳的帧
    size_t bestIndex = 0;
    int64_t minDiff = std::abs(frames_[0].timestamp - targetTimestamp);
    
    // 线性搜索 - 帧数较少时更高效
    for (size_t i = 1; i < frames_.size(); i++) {
        int64_t diff = std::abs(frames_[i].timestamp - targetTimestamp);
        if (diff < minDiff) {
            minDiff = diff;
            bestIndex = i;
        }
    }
    
    // 如果时间差太大，仍然返回最接近的帧，不再失败
    // 这样可以避免因为同步失败而丢弃帧
    
    // 返回最匹配的帧 - 使用引用共享而非深拷贝
    frame = frames_[bestIndex].frame;
    actualTimestamp = frames_[bestIndex].timestamp;
    
    // 只移除这一帧之前的帧，保留后面的帧以便下次匹配
    frames_.erase(frames_.begin(), frames_.begin() + bestIndex + 1);
    
    return true;
}

void FrameBuffer::clear() {
    std::unique_lock<std::mutex> lock(mutex_);
    frames_.clear();
}

size_t FrameBuffer::size() {
    std::unique_lock<std::mutex> lock(mutex_);
    return frames_.size();
}

bool FrameBuffer::getLatestTimestamp(int64_t& timestamp) {
    std::unique_lock<std::mutex> lock(mutex_);
    if (frames_.empty()) {
        return false;
    }
    timestamp = frames_.back().timestamp;
    return true;
}

// MultiCameraManager 实现
MultiCameraManager::MultiCameraManager() 
    : running_(false), 
      syncFrameCount_(0), 
      missedSyncCount_(0), 
      syncMode_(SyncMode::LOW_LATENCY),
      referenceCount_(0) {
}

MultiCameraManager::~MultiCameraManager() {
    stopAll();
}

bool MultiCameraManager::addCamera(const std::string& cameraId, const std::string& cameraName, const CameraConfig& config) {
    std::unique_lock<std::mutex> lock(mutex_);
    
    // 检查相机是否已存在
    if (cameras_.find(cameraId) != cameras_.end()) {
        std::cerr << "相机已存在: " << cameraId << std::endl;
        return false;
    }
    
    // 添加相机信息
    cameras_[cameraId] = CameraInfo(cameraId, cameraName, config);
    
    // 创建帧缓冲区
    buffers_[cameraId] = std::make_unique<FrameBuffer>(3, true);
    
    return true;
}

bool MultiCameraManager::removeCamera(const std::string& cameraId) {
    std::unique_lock<std::mutex> lock(mutex_);
    
    // 检查相机是否存在
    auto cameraIt = cameras_.find(cameraId);
    if (cameraIt == cameras_.end()) {
        std::cerr << "相机不存在: " << cameraId << std::endl;
        return false;
    }
    
    // 如果相机正在运行，先停止
    if (running_) {
        auto threadIt = captureThreads_.find(cameraId);
        if (threadIt != captureThreads_.end()) {
            // 等待线程结束
            if (threadIt->second.joinable()) {
                threadIt->second.join();
            }
            captureThreads_.erase(threadIt);
        }
    }
    
    // 移除相机信息和帧缓冲区
    cameras_.erase(cameraIt);
    buffers_.erase(cameraId);
    
    return true;
}

bool MultiCameraManager::startAll() {
    std::unique_lock<std::mutex> lock(mutex_);
    
    // 检查是否已经在运行
    if (running_) {
        LOG_INFO("相机管理器已在运行中，无需重新启动");
        return true;
    }
    
    // 设置运行标志
    running_ = true;
    
    // 启动所有相机捕获线程
    LOG_INFO("启动所有相机...");
    
    for (const auto& camera : cameras_) {
        const std::string& cameraId = camera.first;
        
        // 创建并启动捕获线程
        captureThreads_[cameraId] = std::thread(&MultiCameraManager::captureThread, this, cameraId);
        LOG_INFO(QString("启动相机捕获线程: %1").arg(QString::fromStdString(cameraId)));
    }
    
    // 创建并启动同步线程
    syncThread_ = std::thread(&MultiCameraManager::syncThread, this);
    LOG_INFO("启动同步线程");
    
    LOG_INFO("所有相机线程已启动");
    return true;
}

void MultiCameraManager::stopAll() {
    std::unique_lock<std::mutex> lock(mutex_);
    
    // 检查是否已经停止
    if (!running_) {
        LOG_INFO("相机管理器已经停止，无需再次停止");
        return;
    }
    
    LOG_INFO("停止所有相机和线程...");
    
    // 设置停止标志
    running_ = false;
    
    // 通知所有等待的线程
    syncCond_.notify_all();
    
    // 释放锁，避免死锁
    lock.unlock();
    
    // 等待同步线程结束
    if (syncThread_.joinable()) {
        LOG_INFO("等待同步线程结束...");
        syncThread_.join();
        LOG_INFO("同步线程已结束");
    }
    
    // 等待所有捕获线程结束
    LOG_INFO("等待所有捕获线程结束...");
    for (auto& thread : captureThreads_) {
        if (thread.second.joinable()) {
            thread.second.join();
            LOG_INFO(QString("捕获线程 %1 已结束").arg(QString::fromStdString(thread.first)));
        }
    }
    
    // 清空线程映射
    captureThreads_.clear();
    
    // 清空所有缓冲区
    for (auto& buffer : buffers_) {
        buffer.second->clear();
    }
    
    LOG_INFO("所有相机和线程已停止");
}

CameraInfo MultiCameraManager::getCameraInfo(const std::string& cameraId) {
    std::unique_lock<std::mutex> lock(mutex_);
    
    auto it = cameras_.find(cameraId);
    if (it != cameras_.end()) {
        return it->second;
    }
    
    return CameraInfo();
}

std::vector<CameraInfo> MultiCameraManager::getAllCameraInfo() {
    std::unique_lock<std::mutex> lock(mutex_);
    
    std::vector<CameraInfo> result;
    result.reserve(cameras_.size());
    
    for (const auto& camera : cameras_) {
        result.push_back(camera.second);
    }
    
    return result;
}

bool MultiCameraManager::getLatestFrame(const std::string& cameraId, cv::Mat& frame, int64_t& timestamp) {
    auto it = buffers_.find(cameraId);
    if (it == buffers_.end()) {
        return false;
    }
    
    return it->second->getLatestFrame(frame, timestamp);
}

bool MultiCameraManager::getSyncFrames(std::map<std::string, cv::Mat>& frames, 
                                      std::map<std::string, int64_t>& timestamps,
                                      int syncThresholdMs,
                                      SyncMode mode) {
    // 清空输出映射
    frames.clear();
    timestamps.clear();
    
    // 如果没有相机，返回失败
    if (cameras_.empty()) {
        return false;
    }
    
    // 根据同步模式获取帧
    if (mode == SyncMode::NO_SYNC) {
        // 完全不同步模式：直接获取每个相机的最新帧
        bool anySuccess = false;
        
        for (const auto& camera : cameras_) {
            cv::Mat frame;
            int64_t timestamp;
            
            if (getLatestFrame(camera.first, frame, timestamp)) {
                frames[camera.first] = frame;
                timestamps[camera.first] = timestamp;
                anySuccess = true;
            }
        }
        
        // 如果至少有一个相机成功获取帧，则计为同步帧
        if (anySuccess) {
            syncFrameCount_++;
            return true;
        }
        
        return false;
    } else if (mode == SyncMode::LOW_LATENCY) {
        // 低延迟模式：直接获取每个相机的帧，不等待同步
        bool anySuccess = false;
        
        for (const auto& buffer : buffers_) {
            cv::Mat frame;
            int64_t timestamp;
            
            if (buffer.second->pop(frame, timestamp, 0)) {
                frames[buffer.first] = frame;
                timestamps[buffer.first] = timestamp;
                anySuccess = true;
            }
        }
        
        // 如果至少有一个相机成功获取帧，则计为同步帧
        if (anySuccess) {
            syncFrameCount_++;
            return true;
        }
        
        return false;
    } else {
        // 同步模式：尝试找到时间上匹配的帧
        // 首先获取所有相机的最新时间戳
        std::map<std::string, int64_t> latestTimestamps;
        
        for (const auto& buffer : buffers_) {
            int64_t timestamp;
            if (buffer.second->getLatestTimestamp(timestamp)) {
                latestTimestamps[buffer.first] = timestamp;
            }
        }
        
        // 如果没有足够的时间戳，返回失败
        if (latestTimestamps.size() < 2) {
            return false;
        }
        
        // 找到时间戳最早的相机
        auto earliestCamera = std::min_element(latestTimestamps.begin(), latestTimestamps.end(),
            [](const auto& a, const auto& b) { return a.second < b.second; });
        
        // 以最早的相机为参考
        cv::Mat referenceFrame;
        int64_t referenceTimestamp;
        
        if (!buffers_[earliestCamera->first]->pop(referenceFrame, referenceTimestamp, 0)) {
            return false;
        }
        
        // 添加参考帧
        frames[earliestCamera->first] = referenceFrame;
        timestamps[earliestCamera->first] = referenceTimestamp;
        
        // 查找其他相机的匹配帧
        bool allMatched = true;
        
        for (const auto& buffer : buffers_) {
            // 跳过参考相机
            if (buffer.first == earliestCamera->first) {
                continue;
            }
            
            cv::Mat frame;
            int64_t timestamp;
            
            if (buffer.second->findClosestFrame(frame, referenceTimestamp, timestamp, syncThresholdMs)) {
                frames[buffer.first] = frame;
                timestamps[buffer.first] = timestamp;
            } else {
                allMatched = false;
                break;
            }
        }
        
        // 如果所有相机都匹配成功，则计为同步帧
        if (allMatched) {
            syncFrameCount_++;
            return true;
        } else {
            // 同步失败，清空已获取的帧
            frames.clear();
            timestamps.clear();
            missedSyncCount_++;
            return false;
        }
    }
}

void MultiCameraManager::setFrameCallback(const std::string& cameraId, 
                                         std::function<void(const cv::Mat&, int64_t)> callback) {
    std::unique_lock<std::mutex> lock(mutex_);
    frameCallbacks_[cameraId] = callback;
}

void MultiCameraManager::setSyncFrameCallback(
    std::function<void(const std::map<std::string, cv::Mat>&, 
                      const std::map<std::string, int64_t>&)> callback) {
    // 检查回调是否为空
    bool isCallbackNull = !callback;
    bool isCurrentCallbackNull = !syncFrameCallback_;
    
    // 如果两者状态相同（都为空或都不为空），则记录日志
    if (isCallbackNull == isCurrentCallbackNull && !isCallbackNull) {
        LOG_INFO("同步帧回调函数未变化，无需重新设置");
        return;
    }
    
    LOG_INFO(QString("设置同步帧回调函数: %1").arg(callback ? "非空回调" : "nullptr"));
    
    // 如果正在运行，需要临时停止同步线程
    bool wasRunning = running_;
    if (wasRunning) {
        LOG_INFO("临时停止同步线程以设置新回调");
        
        // 获取锁
        std::unique_lock<std::mutex> lock(mutex_);
        
        // 设置停止标志
        running_ = false;
        
        // 通知同步线程
        syncCond_.notify_all();
        
        // 释放锁
        lock.unlock();
        
        // 等待同步线程结束
        if (syncThread_.joinable()) {
            syncThread_.join();
        }
        
        LOG_INFO("同步线程已临时停止，准备设置新回调");
    }
    
    // 设置新的回调函数
    {
        std::unique_lock<std::mutex> lock(mutex_);
        syncFrameCallback_ = callback;
    }
    
    // 如果之前在运行，重新启动同步线程
    if (wasRunning) {
        std::unique_lock<std::mutex> lock(mutex_);
        running_ = true;
        syncThread_ = std::thread(&MultiCameraManager::syncThread, this);
        LOG_INFO("重新启动同步线程");
    }
    
    LOG_INFO(QString("同步帧回调函数设置完成: %1").arg(callback ? "非空回调" : "nullptr"));
}

bool MultiCameraManager::isRunning() const {
    return running_;
}

int MultiCameraManager::getSyncFrameCount() const {
    return syncFrameCount_;
}

int MultiCameraManager::getMissedSyncCount() const {
    return missedSyncCount_;
}

void MultiCameraManager::setSyncMode(SyncMode mode) {
    syncMode_ = mode;
}

SyncMode MultiCameraManager::getSyncMode() const {
    return syncMode_;
}

void MultiCameraManager::captureThread(const std::string& cameraId) {
    try {
        // 获取相机信息
        CameraInfo& cameraInfo = cameras_[cameraId];
        std::cout << cameraInfo.name << "线程启动，设备: " << cameraInfo.id << std::endl;
        
        // 相机重连相关变量
        int failureCount = 0;          // 连续失败计数
        const int MAX_FAILURES = 10;   // 最大连续失败次数，超过此值将重连
        bool needReconnect = false;    // 是否需要重连标志
        
        // 打开相机
        cv::VideoCapture camera;
        
        // 相机连接/重连循环
        while (running_) {
            if (!camera.isOpened() || needReconnect) {
                // 如果需要重连或相机未打开，尝试打开相机
                std::cout << "尝试" << (needReconnect ? "重新连接" : "连接") << cameraInfo.name << ": " << cameraInfo.id << std::endl;
                
                // 如果相机已打开，先关闭
                if (camera.isOpened()) {
                    camera.release();
                    std::cout << cameraInfo.name << "已关闭，准备重新连接" << std::endl;
                    // 等待一段时间再重连
                    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
                }
                
                // 设置相机参数 - 在打开前设置
                camera.set(cv::CAP_PROP_FOURCC, cameraInfo.config.fourcc);
                camera.set(cv::CAP_PROP_BUFFERSIZE, cameraInfo.config.bufferSize);
                
                // 打开相机 - 使用V4L2后端
                camera.open(cameraInfo.id, cv::CAP_V4L2);
                
                if (!camera.isOpened()) {
                    std::cerr << "无法打开" << cameraInfo.name << ": " << cameraInfo.id << std::endl;
                    // 等待一段时间再重试
                    std::this_thread::sleep_for(std::chrono::milliseconds(2000));
                    continue;
                }
                
                // 设置相机参数 - 在打开后设置
                // 先设置格式，再设置分辨率和帧率
                camera.set(cv::CAP_PROP_FOURCC, cameraInfo.config.fourcc);
                camera.set(cv::CAP_PROP_FRAME_WIDTH, cameraInfo.config.width);
                camera.set(cv::CAP_PROP_FRAME_HEIGHT, cameraInfo.config.height);
                camera.set(cv::CAP_PROP_FPS, cameraInfo.config.fps);
                
                // 再次确认格式设置
                camera.set(cv::CAP_PROP_FOURCC, cameraInfo.config.fourcc);
                
                // 设置更多高性能参数
                camera.set(cv::CAP_PROP_CONVERT_RGB, cameraInfo.config.convertToRGB ? 1 : 0);
                camera.set(cv::CAP_PROP_BUFFERSIZE, cameraInfo.config.bufferSize);
                
                // 获取实际设置的参数
                int width = camera.get(cv::CAP_PROP_FRAME_WIDTH);
                int height = camera.get(cv::CAP_PROP_FRAME_HEIGHT);
                double fps = camera.get(cv::CAP_PROP_FPS);
                int fourcc = camera.get(cv::CAP_PROP_FOURCC);
                
                char fourcc_str[5] = {0};
                for (int i = 0; i < 4; i++) {
                    fourcc_str[i] = (fourcc >> (i * 8)) & 0xFF;
                }
                
                std::cout << cameraInfo.name << "参数: " << width << "x" << height 
                        << " @ " << fps << "fps, 格式: " << fourcc_str 
                        << ", 缓冲区: " << camera.get(cv::CAP_PROP_BUFFERSIZE) << std::endl;
                
                // 重置失败计数和重连标志
                failureCount = 0;
                needReconnect = false;
            }
            
            // 帧率统计
            int localFrameCount = 0;
            auto lastFpsTime = std::chrono::steady_clock::now();
            
            // 预分配帧缓冲区
            cv::Mat frame;
            
            // 捕获循环
            while (running_ && camera.isOpened() && !needReconnect) {
                try {
                    // 直接读取帧，不分离grab和retrieve过程
                    if (camera.read(frame)) {
                        // 读取成功，重置失败计数
                        failureCount = 0;
                        
                        // 检查帧是否有效
                        if (frame.empty() || frame.rows <= 0 || frame.cols <= 0 || !frame.data) {
                            std::cerr << "相机 " << cameraInfo.name << " 返回了无效帧" << std::endl;
                            failureCount++;
                            std::this_thread::sleep_for(std::chrono::milliseconds(10));
                            continue;
                        }
                        
                        // 获取当前时间戳
                        auto now = std::chrono::system_clock::now().time_since_epoch();
                        int64_t timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(now).count();
                        
                        // 创建帧的副本，避免共享同一块内存
                        cv::Mat frameCopy = frame.clone();
                        
                        // 将帧放入缓冲区
                        if (!buffers_[cameraId]->push(frameCopy, timestamp)) {
                            std::cerr << "无法将帧添加到缓冲区，相机: " << cameraInfo.name << std::endl;
                        }
                        
                        // 调用帧回调函数
                        std::function<void(const cv::Mat&, int64_t)> callback;
                        {
                            std::unique_lock<std::mutex> lock(mutex_);
                            auto callbackIt = frameCallbacks_.find(cameraId);
                            if (callbackIt != frameCallbacks_.end() && callbackIt->second) {
                                callback = callbackIt->second;
                            }
                        }
                        
                        if (callback) {
                            try {
                                callback(frameCopy, timestamp);
                            } catch (const std::exception& e) {
                                std::cerr << "相机 " << cameraInfo.name << " 回调异常: " << e.what() << std::endl;
                            } catch (...) {
                                std::cerr << "相机 " << cameraInfo.name << " 回调未知异常" << std::endl;
                            }
                        }
                        
                        // 更新帧计数
                        cameraInfo.frameCount++;
                        localFrameCount++;
                        
                        // 每秒计算帧率，但不打印
                        auto currentTime = std::chrono::steady_clock::now();
                        auto fpsElapsed = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - lastFpsTime).count();
                        if (fpsElapsed >= 1000) {
                            cameraInfo.fps = localFrameCount * 1000.0f / fpsElapsed;
                            localFrameCount = 0;
                            lastFpsTime = currentTime;
                        }
                    } else {
                        // 读取失败，增加失败计数
                        failureCount++;
                        std::cerr << "相机 " << cameraInfo.name << " 读取帧失败，连续失败次数: " << failureCount << std::endl;
                        
                        // 检查是否需要重连
                        if (failureCount >= MAX_FAILURES) {
                            std::cerr << "相机 " << cameraInfo.name << " 连续读取失败超过阈值，准备重新连接" << std::endl;
                            needReconnect = true;
                            break;
                        }
                        
                        // 非阻塞等待，减少CPU使用
                        std::this_thread::sleep_for(std::chrono::milliseconds(100));
                    }
                } catch (const cv::Exception& e) {
                    std::cerr << "相机 " << cameraInfo.name << " OpenCV异常: " << e.what() << std::endl;
                    failureCount++;
                    
                    // 检查是否需要重连
                    if (failureCount >= MAX_FAILURES) {
                        std::cerr << "相机 " << cameraInfo.name << " 连续异常超过阈值，准备重新连接" << std::endl;
                        needReconnect = true;
                        break;
                    }
                    
                    std::this_thread::sleep_for(std::chrono::milliseconds(100));
                } catch (const std::exception& e) {
                    std::cerr << "相机 " << cameraInfo.name << " 异常: " << e.what() << std::endl;
                    failureCount++;
                    
                    // 检查是否需要重连
                    if (failureCount >= MAX_FAILURES) {
                        std::cerr << "相机 " << cameraInfo.name << " 连续异常超过阈值，准备重新连接" << std::endl;
                        needReconnect = true;
                        break;
                    }
                    
                    std::this_thread::sleep_for(std::chrono::milliseconds(100));
                } catch (...) {
                    std::cerr << "相机 " << cameraInfo.name << " 未知异常" << std::endl;
                    failureCount++;
                    
                    // 检查是否需要重连
                    if (failureCount >= MAX_FAILURES) {
                        std::cerr << "相机 " << cameraInfo.name << " 连续未知异常超过阈值，准备重新连接" << std::endl;
                        needReconnect = true;
                        break;
                    }
                    
                    std::this_thread::sleep_for(std::chrono::milliseconds(100));
                }
            }
        }
        
        // 释放相机
        if (camera.isOpened()) {
            camera.release();
        }
        std::cout << cameraInfo.name << "线程结束" << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "相机线程异常: " << e.what() << std::endl;
    } catch (...) {
        std::cerr << "相机线程未知异常" << std::endl;
    }
}

void MultiCameraManager::syncThread() {
    try {
        std::cout << "同步线程启动" << std::endl;
        
        // 同步状态变量
        int missedSyncCount = 0;
        
        // 计时器
        auto startTime = std::chrono::steady_clock::now();
        auto lastStatusTime = startTime;
        
        // 主循环 - 同步
        while (running_) {
            try {
                // 获取同步帧
                std::map<std::string, cv::Mat> frames;
                std::map<std::string, int64_t> timestamps;
                
                if (getSyncFrames(frames, timestamps, 50, syncMode_)) {
                    // 调用同步帧回调函数
                    std::function<void(const std::map<std::string, cv::Mat>&, 
                                      const std::map<std::string, int64_t>&)> callback;
                    {
                        std::unique_lock<std::mutex> lock(mutex_);
                        callback = syncFrameCallback_;
                    }
                    
                    if (callback) {
                        try {
                            callback(frames, timestamps);
                        } catch (const std::exception& e) {
                            std::cerr << "同步帧回调异常: " << e.what() << std::endl;
                        } catch (...) {
                            std::cerr << "同步帧回调未知异常" << std::endl;
                        }
                    }
                }
                
                // 每秒计算同步状态，但不打印
                auto currentTime = std::chrono::steady_clock::now();
                auto statusElapsed = std::chrono::duration_cast<std::chrono::seconds>(currentTime - lastStatusTime).count();
                if (statusElapsed >= 1) {
                    lastStatusTime = currentTime;
                }
                
                // 短暂休眠，减少CPU使用
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            } catch (const std::exception& e) {
                std::cerr << "同步线程循环异常: " << e.what() << std::endl;
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            } catch (...) {
                std::cerr << "同步线程循环未知异常" << std::endl;
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
        }
        
        std::cout << "同步线程结束" << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "同步线程异常: " << e.what() << std::endl;
    } catch (...) {
        std::cerr << "同步线程未知异常" << std::endl;
    }
}

// 增加相机引用计数
int MultiCameraManager::addReference(const std::string& clientId) {
    std::lock_guard<std::mutex> lock(referenceMutex_);
    
    LOG_INFO(QString("添加引用: %1 当前引用计数: %2").arg(QString::fromStdString(clientId)).arg(referenceCount_));
    
    // 检查客户端是否已经引用
    if (clientReferences_[clientId]) {
        LOG_INFO(QString("客户端已引用，不重复计数: %1").arg(QString::fromStdString(clientId)));
        return referenceCount_;
    }
    
    // 标记客户端已引用
    clientReferences_[clientId] = true;
    
    // 增加引用计数
    int count = ++referenceCount_;
    
    LOG_INFO(QString("引用计数增加，新计数: %1 客户端: %2").arg(count).arg(QString::fromStdString(clientId)));
    
    // 如果是第一个引用，启动相机
    if (count == 1) {
        LOG_INFO("首次引用，启动相机");
        startAll();
    }
    
    return count;
}

// 减少相机引用计数
int MultiCameraManager::removeReference(const std::string& clientId) {
    std::lock_guard<std::mutex> lock(referenceMutex_);
    
    LOG_INFO(QString("移除引用: %1 当前引用计数: %2").arg(QString::fromStdString(clientId)).arg(referenceCount_));
    
    // 检查客户端是否已经引用
    if (!clientReferences_[clientId]) {
        LOG_INFO(QString("客户端未引用，无需移除: %1").arg(QString::fromStdString(clientId)));
        return referenceCount_;
    }
    
    // 标记客户端未引用
    clientReferences_[clientId] = false;
    
    // 减少引用计数
    int count = --referenceCount_;
    
    LOG_INFO(QString("引用计数减少，新计数: %1 客户端: %2").arg(count).arg(QString::fromStdString(clientId)));
    
    // 如果没有引用了，停止相机
    if (count == 0) {
        LOG_INFO("无引用，停止相机");
        stopAll();
    }
    
    return count;
}

// 获取当前引用计数
int MultiCameraManager::getReferenceCount() const {
    return referenceCount_;
}

bool MultiCameraManager::setCameraParameters(const QString& cameraId, const QMap<QString, QVariant>& parameters) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    LOG_INFO(QString("设置相机参数，相机ID: %1").arg(cameraId));
    
    // 检查相机ID是否有效
    if (cameraId.isEmpty()) {
        LOG_WARNING("无效的相机ID: 空字符串");
        return false;
    }
    
    // 将QString转换为std::string
    std::string cameraIdStr = cameraId.toStdString();
    
    // 检查相机是否存在
    if (cameras_.find(cameraIdStr) == cameras_.end()) {
        LOG_WARNING(QString("相机不存在于管理器中，ID: %1").arg(cameraId));
        // 即使相机不在管理器中，我们也尝试直接设置参数
    }
    
    // 检查相机管理器是否运行
    if (!isRunning()) {
        LOG_WARNING(QString("相机管理器未运行，但仍将尝试设置参数，相机ID: %1").arg(cameraId));
        // 即使管理器未运行，我们也尝试直接设置参数
    }
    
    try {
        // 获取OpenCV相机设备ID
        int deviceId = -1;
        
        // 尝试多种方式解析设备ID
        try {
            // 方法1: 尝试将整个ID直接转换为数字
            bool ok = false;
            deviceId = cameraId.toInt(&ok);
            
            // 方法2: 如果是/dev/videoX格式
            if (!ok && cameraId.contains("/dev/video")) {
                QString devicePath = cameraId;
                devicePath = devicePath.replace("/dev/video", "");
                deviceId = devicePath.toInt(&ok);
                LOG_DEBUG(QString("从/dev/video格式解析设备ID: %1，成功: %2").arg(deviceId).arg(ok));
            }
            
            // 方法3: 如果是纯数字字符串
            if (!ok && cameraId.trimmed().toInt(&ok) >= 0) {
                deviceId = cameraId.trimmed().toInt();
                LOG_DEBUG(QString("从纯数字字符串解析设备ID: %1").arg(deviceId));
            }
            
            // 方法4: 如果是路径的最后一个数字
            if (!ok) {
                QRegExp rx("(\\d+)$");
                if (rx.indexIn(cameraId) != -1) {
                    QString numStr = rx.cap(1);
                    deviceId = numStr.toInt(&ok);
                    LOG_DEBUG(QString("从路径最后一个数字解析设备ID: %1").arg(deviceId));
                }
            }
            
            LOG_DEBUG(QString("解析相机ID: %1 得到设备ID: %2").arg(cameraId).arg(deviceId));
        } catch (const std::exception& e) {
            LOG_WARNING(QString("解析相机ID时发生异常: %1").arg(e.what()));
            deviceId = -1;
        } catch (...) {
            LOG_WARNING("解析相机ID时发生未知异常");
            deviceId = -1;
        }
        
        // 如果无法解析设备ID，尝试使用原始ID
        if (deviceId < 0) {
            LOG_WARNING(QString("无法解析为数字设备ID，尝试使用原始ID: %1").arg(cameraId));
            
            // 创建临时VideoCapture对象，尝试使用原始ID
            cv::VideoCapture camera;
            bool opened = false;
            
            // 尝试使用原始ID打开
            try {
                opened = camera.open(cameraIdStr, cv::CAP_V4L2);
            } catch (...) {
                opened = false;
            }
            
            if (!opened) {
                LOG_WARNING(QString("无法使用原始ID打开相机: %1").arg(cameraId));
                return false;
            }
            
            // 应用参数
            for (auto it = parameters.begin(); it != parameters.end(); ++it) {
                QString paramName = it.key();
                QVariant value = it.value();
                
                LOG_DEBUG(QString("设置参数: %1 = %2").arg(paramName).arg(value.toString()));
                
                // 根据参数名称设置相机属性
                if (paramName == "brightness") {
                    camera.set(cv::CAP_PROP_BRIGHTNESS, value.toInt());
                } else if (paramName == "contrast") {
                    camera.set(cv::CAP_PROP_CONTRAST, value.toInt());
                } else if (paramName == "saturation") {
                    camera.set(cv::CAP_PROP_SATURATION, value.toInt());
                } else if (paramName == "gamma") {
                    camera.set(cv::CAP_PROP_GAMMA, value.toInt());
                } else if (paramName == "gain") {
                    camera.set(cv::CAP_PROP_GAIN, value.toInt());
                } else if (paramName == "exposure_time") {
                    camera.set(cv::CAP_PROP_EXPOSURE, value.toInt());
                } else if (paramName == "auto_exposure") {
                    camera.set(cv::CAP_PROP_AUTO_EXPOSURE, value.toBool() ? 3 : 1);
                } else if (paramName == "auto_white_balance") {
                    camera.set(cv::CAP_PROP_AUTO_WB, value.toBool() ? 1 : 0);
                } else {
                    LOG_WARNING(QString("未知的相机参数: %1").arg(paramName));
                }
            }
            
            // 关闭临时相机对象
            camera.release();
            
            LOG_INFO(QString("相机参数设置成功，相机ID: %1").arg(cameraId));
            return true;
        }
        
        // 使用解析出的设备ID创建临时VideoCapture对象
        LOG_DEBUG(QString("使用设备ID打开相机: %1").arg(deviceId));
        
        // 尝试多种方式打开相机
        cv::VideoCapture camera;
        bool opened = false;
        
        // 方法1: 使用数字索引
        try {
            opened = camera.open(deviceId, cv::CAP_V4L2);
            LOG_DEBUG(QString("使用数字索引打开相机: %1").arg(opened ? "成功" : "失败"));
        } catch (...) {
            opened = false;
        }
        
        // 方法2: 使用完整路径
        if (!opened) {
            try {
                QString fullPath = QString("/dev/video%1").arg(deviceId);
                LOG_DEBUG(QString("尝试使用完整路径打开相机: %1").arg(fullPath));
                opened = camera.open(fullPath.toStdString(), cv::CAP_V4L2);
                LOG_DEBUG(QString("使用完整路径打开相机: %1").arg(opened ? "成功" : "失败"));
            } catch (...) {
                opened = false;
            }
        }
        
        // 方法3: 使用原始ID
        if (!opened) {
            try {
                LOG_DEBUG(QString("尝试使用原始ID打开相机: %1").arg(cameraId));
                opened = camera.open(cameraIdStr, cv::CAP_V4L2);
                LOG_DEBUG(QString("使用原始ID打开相机: %1").arg(opened ? "成功" : "失败"));
            } catch (...) {
                opened = false;
            }
        }
        
        if (!opened) {
            LOG_WARNING(QString("无法打开相机设备，ID: %1").arg(deviceId));
            return false;
        }
        
        // 遍历参数并应用
        for (auto it = parameters.begin(); it != parameters.end(); ++it) {
            QString paramName = it.key();
            QVariant value = it.value();
            
            LOG_DEBUG(QString("设置参数: %1 = %2").arg(paramName).arg(value.toString()));
            
            // 根据参数名称设置相机属性
            if (paramName == "brightness") {
                camera.set(cv::CAP_PROP_BRIGHTNESS, value.toInt());
            } else if (paramName == "contrast") {
                camera.set(cv::CAP_PROP_CONTRAST, value.toInt());
            } else if (paramName == "saturation") {
                camera.set(cv::CAP_PROP_SATURATION, value.toInt());
            } else if (paramName == "gamma") {
                camera.set(cv::CAP_PROP_GAMMA, value.toInt());
            } else if (paramName == "gain") {
                camera.set(cv::CAP_PROP_GAIN, value.toInt());
            } else if (paramName == "exposure_time") {
                camera.set(cv::CAP_PROP_EXPOSURE, value.toInt());
            } else if (paramName == "auto_exposure") {
                camera.set(cv::CAP_PROP_AUTO_EXPOSURE, value.toBool() ? 3 : 1);
            } else if (paramName == "auto_white_balance") {
                camera.set(cv::CAP_PROP_AUTO_WB, value.toBool() ? 1 : 0);
            } else {
                LOG_WARNING(QString("未知的相机参数: %1").arg(paramName));
            }
        }
        
        // 关闭临时相机对象
        camera.release();
        
        LOG_INFO(QString("相机参数设置成功，相机ID: %1").arg(cameraId));
        return true;
    } catch (const cv::Exception& e) {
        LOG_WARNING(QString("设置相机参数时发生OpenCV异常: %1").arg(e.what()));
        return false;
    } catch (const std::exception& e) {
        LOG_WARNING(QString("设置相机参数时发生异常: %1").arg(e.what()));
        return false;
    } catch (...) {
        LOG_WARNING("设置相机参数时发生未知异常");
        return false;
    }
}

} // namespace CameraUtils
} // namespace Core
} // namespace SmartScope 