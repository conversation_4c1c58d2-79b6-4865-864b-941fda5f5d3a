#include "core/camera/camera.h"
#include <QDebug>

namespace SmartScope {
namespace Core {

Camera::Camera(const QString& deviceId, QObject* parent)
    : QObject(parent)
    , m_deviceId(deviceId)
    , m_isOpened(false)
    , m_resolution(1280, 720)
    , m_frameRate(30.0)
    , m_frameCount(0)
{
    m_fpsTimer.start();
    qDebug() << "Camera: 创建相机实例，设备ID:" << deviceId;
}

Camera::~Camera()
{
    qDebug() << "Camera: 销毁相机实例，设备ID:" << m_deviceId;
}

QString Camera::getDeviceId() const
{
    return m_deviceId;
}

} // namespace Core
} // namespace SmartScope 