#pragma once

#include <string>
#include <vector>
#include <memory>
#include <map>
#include <opencv2/opencv.hpp>
#include <onnxruntime_cxx_api.h>
// 添加PCL相关头文件
#include <pcl/io/pcd_io.h>
#include <pcl/io/ply_io.h>
#include <pcl/point_types.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/filters/statistical_outlier_removal.h>
#include <pcl/filters/radius_outlier_removal.h>
#include <pcl/sample_consensus/method_types.h>
#include <pcl/sample_consensus/model_types.h>
#include <pcl/segmentation/sac_segmentation.h>
#include <pcl/filters/extract_indices.h>
#include <pcl/filters/project_inliers.h>
#include <pcl/common/transforms.h>
#include <pcl/console/time.h>

// 点云过滤参数结构体
struct PointCloudFilterParams {
    float voxel_size = 0.015f;      // 体素大小
    float min_depth = 0.5f;         // 最小深度
    float max_depth = 30.0f;        // 最大深度
    int mean_k = 30;                // 统计滤波邻域点数
    float std_dev_mul = 1.2f;       // 统计滤波标准差倍数
    float radius = 0.05f;           // 半径滤波搜索半径
    int min_neighbors = 10;         // 半径滤波最小邻居数量
};

class StereoInference {
public:
    // 性能模式枚举
    enum PerformanceMode {
        HIGH_QUALITY,   // 高质量模式
        BALANCED,       // 平衡模式
        FAST,           // 快速模式
        ULTRA_FAST      // 超快模式
    };
    
    // 固定的输入图像尺寸
    static constexpr int INPUT_WIDTH = 720;
    static constexpr int INPUT_HEIGHT = 1280;
    
    // 固定的裁剪和缩放尺寸
    static constexpr int CROP_WIDTH = 256;
    static constexpr int CROP_HEIGHT = 256;
    static constexpr int MODEL_WIDTH = 256;
    static constexpr int MODEL_HEIGHT = 256;
    
    // 固定的通道数
    static constexpr int CHANNELS = 3;
    
    // 固定的内存对齐值
    static constexpr int DIVIS_BY = 32;
    
    // 固定的预处理参数
    static constexpr float MEAN[3] = {0.485f, 0.456f, 0.406f};
    static constexpr float STD[3] = {0.229f, 0.224f, 0.225f};

    StereoInference(const std::string& model_path);
    ~StereoInference();

    // 性能模式设置
    void setPerformanceMode(PerformanceMode mode);
    PerformanceMode getPerformanceMode() const { return perf_mode_; }

    // 推理函数
    cv::Mat inference(const cv::Mat& left_img, const cv::Mat& right_img);

    // 保存结果
    void saveDisparity(const cv::Mat& disparity, const std::string& filename);
    void savePointCloud(const cv::Mat& disparity, const cv::Mat& color, 
                       const std::string& filename,
                       float baseline = 0.23f, float focal_length = -1.0f);
                       
    // 比较函数
    void compareWithReference(const cv::Mat& disparity, const std::string& ref_npy,
                            const std::string& output_dir);
    
    // 辅助函数
    std::pair<cv::Mat, cv::Mat> pad_images(const cv::Mat& img1, const cv::Mat& img2);
    cv::Mat unpad_disparity(const cv::Mat& disparity, const cv::Size& original_size);
    cv::Mat crop_center_height(const cv::Mat& img, int target_height);
    std::vector<float> preprocess(const cv::Mat& img);
    
    // 新增预处理函数
    cv::Mat preprocessCropFixed(const cv::Mat& img);
    
    // 点云过滤和优化函数
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr voxelGridFilter(
        const pcl::PointCloud<pcl::PointXYZRGB>::Ptr& cloud,
        float leaf_size);
        
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr statisticalOutlierRemoval(
        const pcl::PointCloud<pcl::PointXYZRGB>::Ptr& cloud,
        int mean_k,
        float std_dev_mul);
        
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr radiusOutlierRemoval(
        const pcl::PointCloud<pcl::PointXYZRGB>::Ptr& cloud,
        float radius,
        int min_neighbors);
        
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr depthRangeFilter(
        const pcl::PointCloud<pcl::PointXYZRGB>::Ptr& cloud,
        float min_depth,
        float max_depth);
        
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr filterPointCloud(
        const pcl::PointCloud<pcl::PointXYZRGB>::Ptr& cloud,
        const PointCloudFilterParams& params);
        
    bool filterPointCloudFile(
        const std::string& input_file,
        const std::string& output_file,
        const PointCloudFilterParams& params);
        
    // 点云优化函数
    bool saveOptimizedPointCloud(
        const std::string& input_file,
        const std::string& output_file,
        double distance_threshold = 0.02,
        bool project_to_plane = false);

private:
    // 预处理函数
    void preprocess_small(const cv::Mat& img, std::vector<float>& output);
    
    // 后处理函数
    cv::Mat postprocess(const std::vector<float>& output, 
                       const std::vector<int64_t>& output_shape);
                       
    // 加载NPY文件
    cv::Mat loadNpy(const std::string& filename);
    
    // 更新输入尺寸
    void updateInputShape();
    
    // 优化的辅助函数
    void optimizeMemoryLayout();

    // ONNX Runtime相关
    Ort::Env env_;
    Ort::Session session_{nullptr};
    Ort::MemoryInfo memory_info_{nullptr};
    std::vector<std::string> input_names_;
    std::vector<std::string> output_names_;
    std::vector<int64_t> input_shape_;
    
    // 预分配的输入缓冲区
    std::vector<float> input_buffer1_;
    std::vector<float> input_buffer2_;
    
    // 裁剪信息
    cv::Rect crop_roi_;
    
    // 性能模式
    PerformanceMode perf_mode_ = BALANCED;
    
    // 模型尺寸
    int width_ = MODEL_WIDTH;
    int height_ = MODEL_HEIGHT;
    int channels_ = CHANNELS;
    int divis_by_ = DIVIS_BY;
    
    // 默认裁剪尺寸
    int crop_width_ = CROP_WIDTH;
    int crop_height_ = CROP_HEIGHT;
}; 