#include "inference/stereo_inference.hpp"
#include "inference/lightstereo_inference.hpp"
#include <fstream>
#include <iostream>
#include <chrono>
#include <map>
#include <vector>
#include <memory>
#include <thread>
#include <future>
#include <omp.h>
#include <pcl/io/ply_io.h>
#include <pcl/point_types.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/filters/statistical_outlier_removal.h>
#include <pcl/filters/radius_outlier_removal.h>
#include <pcl/filters/extract_indices.h>
#include <pcl/filters/project_inliers.h>
#include <pcl/ModelCoefficients.h>
#include <pcl/console/time.h>
#include <Eigen/Dense>

// 性能模式对应的分辨率
const std::map<StereoInference::PerformanceMode, std::pair<int, int>> resolution_map = {
    {StereoInference::HIGH_QUALITY, {736, 736}},
    {StereoInference::BALANCED, {512, 512}},
    {StereoInference::FAST, {384, 384}},
    {StereoInference::ULTRA_FAST, {256, 256}}
};

// 设置OpenMP线程数
const int NUM_THREADS = 8;

StereoInference::StereoInference(const std::string& model_path)
    : model_path_(model_path) {
    
    try {
        // 设置OpenMP线程数
        omp_set_num_threads(NUM_THREADS);

        std::cout << "Initializing StereoInference with LightStereo backend..." << std::endl;

        // 初始化性能模式到模型的映射
        // 如果没有提供模型路径，使用默认路径
        std::string base_path = model_path_.empty() ? "models/" : model_path_;

        model_map_[HIGH_QUALITY] = base_path + "lightstereo_s_sceneflow_general_opt_576_960.rknn";
        model_map_[BALANCED] = base_path + "lightstereo_s_sceneflow_general_opt_576_960.rknn";
        model_map_[FAST] = base_path + "lightstereo_s_sceneflow_general_opt_256_512.rknn";
        model_map_[ULTRA_FAST] = base_path + "lightstereo_s_sceneflow_general_opt_256_512.rknn";

        // 设置为ULTRA_FAST模式
        setPerformanceMode(ULTRA_FAST);

        // 初始化 LightStereo 引擎
        if (!initializeLightStereo()) {
            throw std::runtime_error("Failed to initialize LightStereo engine");
        }

        // 更新输入shape（保持兼容性）
        updateInputShape();

        // 预分配内存缓冲区（保持兼容性）
        optimizeMemoryLayout();

        std::cout << "StereoInference initialized successfully with LightStereo backend" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "Error initializing StereoInference: " << e.what() << std::endl;
        throw;
    }
}

StereoInference::~StereoInference() {}

bool StereoInference::initializeLightStereo() {
    std::string model_path = getModelPathForMode(perf_mode_);
    std::cout << "Initializing LightStereo with model: " << model_path << std::endl;

    lightstereo_engine_ = std::make_unique<lightstereo_inference::LightStereoInference>(model_path);

    if (!lightstereo_engine_->Initialize()) {
        std::cerr << "Failed to initialize LightStereo engine" << std::endl;
        return false;
    }

    std::cout << "LightStereo engine initialized successfully" << std::endl;
    return true;
}

std::string StereoInference::getModelPathForMode(PerformanceMode mode) {
    auto it = model_map_.find(mode);
    if (it != model_map_.end()) {
        return it->second;
    }

    // 默认返回高质量模型
    return model_map_[HIGH_QUALITY];
}

cv::Mat StereoInference::adaptLightStereoOutput(const cv::Mat& lightstereo_disparity,
                                                const cv::Size& target_size,
                                                const lightstereo_inference::ScaleInfo& scale_info) {
    if (lightstereo_disparity.empty()) {
        std::cerr << "LightStereo output is empty!" << std::endl;
        return cv::Mat();
    }

    cv::Mat adapted_disparity;

    // 如果目标尺寸与输出尺寸不同，需要缩放
    if (lightstereo_disparity.size() != target_size) {
        cv::resize(lightstereo_disparity, adapted_disparity, target_size, 0, 0, cv::INTER_LINEAR);

        // 缩放视差值
        float scale_factor = static_cast<float>(target_size.width) / lightstereo_disparity.cols;
        adapted_disparity *= scale_factor;

        std::cout << "Adapted disparity from " << lightstereo_disparity.size()
                  << " to " << target_size << ", scale factor: " << scale_factor << std::endl;
    } else {
        adapted_disparity = lightstereo_disparity.clone();
    }

    return adapted_disparity;
}

void StereoInference::setPerformanceMode(PerformanceMode mode) {
    if (perf_mode_ != mode) {
        perf_mode_ = mode;

        // 更新分辨率
        auto res_iter = resolution_map.find(perf_mode_);
        if (res_iter != resolution_map.end()) {
            width_ = res_iter->second.first;
            height_ = res_iter->second.second;
        }

        // 重新初始化 LightStereo 引擎（如果已经初始化过）
        if (lightstereo_engine_) {
            std::cout << "Switching performance mode to: " << mode << std::endl;
            initializeLightStereo();
        }

        // 更新输入shape（保持兼容性）
        updateInputShape();
    }
}

void StereoInference::updateInputShape() {
    std::cout << "Updating input shape for LightStereo..." << std::endl;

    // LightStereo 使用固定的输入形状，根据性能模式调整
    if (lightstereo_engine_) {
        auto input_size = lightstereo_engine_->GetInputSize();
        width_ = input_size.first;
        height_ = input_size.second;

        std::cout << "LightStereo input size: " << width_ << "x" << height_ << std::endl;
    } else {
        // 如果引擎未初始化，使用默认值
        std::cout << "LightStereo engine not initialized, using default size: "
                  << width_ << "x" << height_ << std::endl;
    }
}

void StereoInference::optimizeMemoryLayout() {
    // LightStereo 内部管理内存，这里保持接口兼容性
    std::cout << "Memory layout optimized for LightStereo backend" << std::endl;

    // 预计算裁剪区域（保持兼容性）
    crop_roi_ = cv::Rect(0, (INPUT_HEIGHT - CROP_HEIGHT) / 2, CROP_WIDTH, CROP_HEIGHT);
}

std::pair<cv::Mat, cv::Mat> StereoInference::pad_images(const cv::Mat& img1, const cv::Mat& img2) {
    // 由于输入已经调整为正确尺寸，直接返回原图，保持接口兼容性
    return {img1.clone(), img2.clone()};
}

cv::Mat StereoInference::unpad_disparity(const cv::Mat& disparity, const cv::Size& original_size) {
    // 由于输入不再需要解填充，直接返回原图，保持接口兼容性
    return disparity.clone();
}

cv::Mat StereoInference::crop_center_height(const cv::Mat& img, int target_height) {
    // 简化实现，使用正方形裁剪逻辑替代高度裁剪
    int size = std::min(img.cols, img.rows);
    if (size <= target_height) {
        crop_roi_ = cv::Rect(0, 0, img.cols, img.rows);
        return img.clone();
    }
    
    int start_x = (img.cols - size) / 2;
    int start_y = (img.rows - size) / 2;
    crop_roi_ = cv::Rect(start_x, start_y, size, size);
    return img(crop_roi_).clone();
}

// 实现新的预处理裁剪函数
cv::Mat StereoInference::preprocessCropFixed(const cv::Mat& img) {
    // 获取原始图像尺寸
    cv::Size original_size = img.size();
    
    // 检查图像有效性
    if (original_size.width <= 0 || original_size.height <= 0) {
        throw std::runtime_error("无效的图像尺寸");
    }
    
    // 要裁剪的目标尺寸
    const int target_width = 720;
    const int target_height = 1080;
    
    // 智能居中裁剪策略：
    // 始终使用居中裁剪，确保裁剪区域位于图像中心
    int start_x, start_y, crop_width, crop_height;

    // 计算裁剪尺寸（不超过原图尺寸）
    crop_width = std::min(target_width, original_size.width);
    crop_height = std::min(target_height, original_size.height);

    // 计算居中裁剪的起始位置
    start_x = std::max(0, (original_size.width - crop_width) / 2);
    start_y = std::max(0, (original_size.height - crop_height) / 2);

    // 确保裁剪区域不超出图像边界
    if (start_x + crop_width > original_size.width) {
        start_x = original_size.width - crop_width;
    }
    if (start_y + crop_height > original_size.height) {
        start_y = original_size.height - crop_height;
    }

    std::cout << "preprocessCropFixed: 原始尺寸 " << original_size.width << "x" << original_size.height
              << " → 裁剪区域 (" << start_x << "," << start_y << "," << crop_width << "," << crop_height << ")" << std::endl;
    
    // 更新裁剪区域信息，用于后续结果还原
    crop_roi_ = cv::Rect(start_x, start_y, crop_width, crop_height);
    std::cout << "保存裁剪ROI: (" << start_x << "," << start_y << "," << crop_width << "," << crop_height << ")" << std::endl;
    
    // 返回裁剪后的图像
    cv::Mat cropped = img(crop_roi_).clone();
    
    // 如果裁剪后的尺寸与目标尺寸不一致（原图太小），则调整大小
    if (cropped.size().width != target_width || cropped.size().height != target_height) {
        cv::Mat resized;
        cv::resize(cropped, resized, cv::Size(target_width, target_height), 0, 0, cv::INTER_AREA);
        return resized;
    }
    
    return cropped;
}

std::vector<float> StereoInference::preprocess(const cv::Mat& img) {
    try {
        // 确保输入是RGB格式
        cv::Mat rgb_img;
        if (img.channels() == 3) {
            cv::cvtColor(img, rgb_img, cv::COLOR_BGR2RGB);
        } else {
            throw std::runtime_error("Input image must have 3 channels");
        }
        
        // 转换为float (归一化到0-1)
        cv::Mat float_img;
        rgb_img.convertTo(float_img, CV_32F, 1.0/255.0);
        
        // 转换为NCHW格式
        std::vector<float> input_tensor(1 * channels_ * float_img.rows * float_img.cols);
        float* input_ptr = input_tensor.data();
        
        // 使用OpenMP进行并行处理
        #pragma omp parallel for collapse(2) num_threads(NUM_THREADS)
        for (int h = 0; h < float_img.rows; h++) {
            for (int w = 0; w < float_img.cols; w++) {
                for (int c = 0; c < channels_; c++) {
                    float pixel_value = float_img.at<cv::Vec3f>(h, w)[c];
                    // 归一化
                    float normalized_value = (pixel_value - MEAN[c]) / STD[c];
                    input_ptr[c * float_img.rows * float_img.cols + h * float_img.cols + w] = normalized_value;
                }
            }
        }
        
        return input_tensor;
        
    } catch (const std::exception& e) {
        std::cerr << "Error in preprocess: " << e.what() << std::endl;
        throw;
    }
}

cv::Mat StereoInference::postprocess(const std::vector<float>& output,
                                   const std::vector<int64_t>& output_shape) {
    try {
        // 检查输出shape
        if (output_shape.size() != 4) {
            throw std::runtime_error("Expected 4D output tensor (N,C,H,W)");
        }
        
        int batch_size = output_shape[0];
        int channels = output_shape[1];
        int height = output_shape[2];
        int width = output_shape[3];
        
        if (batch_size != 1 || channels != 1) {
            throw std::runtime_error("Expected output shape [1,1,H,W]");
        }
        
        // 检查输出数据范围（与inference实现一致）
        float tensor_min = *std::min_element(output.begin(), output.end());
        float tensor_max = *std::max_element(output.begin(), output.end());
        
        // 转换为OpenCV格式
        cv::Mat disparity(height, width, CV_32F);
        
        // 使用OpenMP并行化后处理（保留性能优化）
        float* disp_ptr = (float*)disparity.data;
        
        #pragma omp parallel for collapse(2) num_threads(NUM_THREADS)
        for (int h = 0; h < height; h++) {
            for (int w = 0; w < width; w++) {
                disp_ptr[h * width + w] = output[h * width + w];
            }
        }
        
        // 检查视差图范围
        double min_val, max_val;
        cv::Point min_loc, max_loc;
        cv::minMaxLoc(disparity, &min_val, &max_val, &min_loc, &max_loc);
        
        return disparity;
        
    } catch (const std::exception& e) {
        std::cerr << "Error in postprocess: " << e.what() << std::endl;
        throw;
    }
}

cv::Mat StereoInference::inference(const cv::Mat& left_img, const cv::Mat& right_img) {
    try {
        auto total_start_time = std::chrono::high_resolution_clock::now();

        // 1. 检查输入图像尺寸是否一致
        if (left_img.size() != right_img.size()) {
            std::cerr << "左右图像尺寸不一致: 左图 " << left_img.size().width << "x" << left_img.size().height
                     << ", 右图 " << right_img.size().width << "x" << right_img.size().height << std::endl;
            throw std::runtime_error("左右图像尺寸不一致");
        }

        // 2. 检查 LightStereo 引擎是否已初始化
        if (!lightstereo_engine_) {
            std::cerr << "LightStereo engine not initialized!" << std::endl;
            throw std::runtime_error("LightStereo engine not initialized");
        }

        std::cout << "使用 LightStereo 进行推理，输入图像尺寸: " << left_img.size() << std::endl;

        // 3. 使用 LightStereo 进行推理
        cv::Mat disparity_map;
        lightstereo_inference::ScaleInfo scale_info;

        auto inf_start_time = std::chrono::high_resolution_clock::now();

        bool success = lightstereo_engine_->InferenceWithScaleInfo(
            left_img, right_img, disparity_map, scale_info);

        auto inf_end_time = std::chrono::high_resolution_clock::now();
        auto inf_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
            inf_end_time - inf_start_time).count();

        if (!success) {
            std::cerr << "LightStereo inference failed!" << std::endl;
            throw std::runtime_error("LightStereo inference failed");
        }

        std::cout << "LightStereo 推理时间: " << inf_duration << " ms" << std::endl;
        std::cout << "输出视差图尺寸: " << disparity_map.size() << std::endl;
        std::cout << "缩放因子: " << scale_info.scale_factor << std::endl;

        // 4. 适配输出到期望的格式
        cv::Mat adapted_disparity = adaptLightStereoOutput(disparity_map, left_img.size(), scale_info);

        auto total_end_time = std::chrono::high_resolution_clock::now();
        auto total_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
            total_end_time - total_start_time).count();

        std::cout << "总处理时间: " << total_duration << " ms" << std::endl;

        return adapted_disparity;

    } catch (const std::exception& e) {
        std::cerr << "Error in LightStereo inference: " << e.what() << std::endl;
        throw;
    }
}

void StereoInference::preprocess_small(const cv::Mat& img, std::vector<float>& output) {
    // 直接使用BGR格式，避免不必要的颜色空间转换
    cv::Mat float_img;
    img.convertTo(float_img, CV_32F, 1.0/255.0);
    
    // 使用指针直接访问数据以提高性能
    float* output_ptr = output.data();
    float* img_ptr = (float*)float_img.data;
    
    // 获取实际尺寸
    int img_height = float_img.rows;
    int img_width = float_img.cols;
    
    // 优化的预处理循环，使用8个线程
    #pragma omp parallel for collapse(2) num_threads(NUM_THREADS)
    for (int h = 0; h < img_height; h++) {
        for (int w = 0; w < img_width; w++) {
            for (int c = 0; c < CHANNELS; c++) {
                // 归一化并存储
                output_ptr[c * img_height * img_width + h * img_width + w] = 
                    (img_ptr[h * img_width * CHANNELS + w * CHANNELS + c] - MEAN[c]) / STD[c];
            }
        }
    }
}

void StereoInference::saveDisparity(const cv::Mat& disparity, const std::string& filename) {
    try {
        // 计算视差图的最小最大值
        double min_val, max_val;
        cv::Point min_loc, max_loc;
        cv::minMaxLoc(disparity, &min_val, &max_val, &min_loc, &max_loc);
        
        std::cout << "Disparity range: [" << min_val << ", " << max_val << "]" << std::endl;
        
        // 归一化到0-255
        cv::Mat normalized;
        cv::normalize(disparity, normalized, 0, 255, cv::NORM_MINMAX);
        normalized.convertTo(normalized, CV_8U);
        
        // 应用colormap (与matplotlib的jet相同)
        cv::Mat colored;
        cv::applyColorMap(normalized, colored, cv::COLORMAP_JET);
        
        // 保存
        cv::imwrite(filename, colored);
        std::cout << "Saved disparity visualization to: " << filename << std::endl;
        
        // 保存原始视差数据
        std::string raw_filename = filename.substr(0, filename.find_last_of('.')) + ".npy";
        std::ofstream out(raw_filename, std::ios::binary);
        if (out.is_open()) {
            // 写入简单的头部
            const char header[] = "\x93NUMPY\x01\x00v\x00";
            out.write(header, 8);
            
            // 写入shape信息
            std::stringstream header_stream;
            header_stream << "{'descr': '<f4', 'fortran_order': False, 'shape': (" 
                         << disparity.rows << ", " << disparity.cols << "), }";
            std::string header_str = header_stream.str();
            while ((header_str.length() + 10) % 16 != 0) {
                header_str += ' ';
            }
            header_str += "\n";
            
            uint16_t header_len = header_str.length();
            out.write(reinterpret_cast<char*>(&header_len), sizeof(header_len));
            out.write(header_str.c_str(), header_str.length());
            
            // 写入数据
            out.write(reinterpret_cast<const char*>(disparity.data), 
                     disparity.rows * disparity.cols * sizeof(float));
            out.close();
            
            std::cout << "Saved raw disparity data to: " << raw_filename << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cerr << "Error in saveDisparity: " << e.what() << std::endl;
        throw;
    }
}

void StereoInference::savePointCloud(const cv::Mat& disparity, const cv::Mat& color,
                                   const std::string& filename,
                                   float baseline, float focal_length) {
    try {
        // 基本参数获取与检查
        int disp_height = disparity.rows;  // 视差图高度 (已缩放回裁剪尺寸)
        int disp_width = disparity.cols;   // 视差图宽度 (已缩放回裁剪尺寸)
        int orig_height = color.rows;      // 原始图像高度
        int orig_width = color.cols;       // 原始图像宽度
        
        std::cout << "点云生成参数:" << std::endl;
        std::cout << "  原始图像尺寸: " << orig_width << "x" << orig_height << std::endl;
        std::cout << "  视差图尺寸: " << disp_width << "x" << disp_height << std::endl;
        std::cout << "  裁剪区域: " << crop_roi_.x << "," << crop_roi_.y << "," 
                 << crop_roi_.width << "x" << crop_roi_.height << std::endl;
        
        // 保存参与计算的原始图像
        cv::imwrite("pc_color_orig.jpg", color);
        
        // 检查裁剪ROI有效性
        if (crop_roi_.width <= 0 || crop_roi_.height <= 0 ||
            crop_roi_.x < 0 || crop_roi_.y < 0 ||
            crop_roi_.x + crop_roi_.width > orig_width ||
            crop_roi_.y + crop_roi_.height > orig_height) {
            
            std::cerr << "无效的裁剪区域: " << crop_roi_ << std::endl;
            
            // 在ROI无效时，使用正方形裁剪逻辑
            int crop_size = std::min(orig_width, orig_height);
            int start_x = (orig_width - crop_size) / 2;
            int start_y = (orig_height - crop_size) / 2;
            
            crop_roi_ = cv::Rect(start_x, start_y, crop_size, crop_size);
            std::cout << "  重新计算裁剪区域: " << crop_roi_.x << "," << crop_roi_.y << "," 
                     << crop_roi_.width << "x" << crop_roi_.height << std::endl;
        }
        
        // 检查视差图尺寸与裁剪区域是否匹配
        if (disp_width != crop_roi_.width || disp_height != crop_roi_.height) {
            std::cout << "  警告: 视差图尺寸(" << disp_width << "x" << disp_height 
                     << ")与裁剪尺寸(" << crop_roi_.width << "x" << crop_roi_.height << ")不匹配" << std::endl;
            
            // 如果视差图大小与裁剪区域不匹配，调整裁剪区域
            if (disp_width == disp_height && crop_roi_.width == crop_roi_.height) {
                // 保持正方形，只调整大小
                crop_roi_.width = disp_width;
                crop_roi_.height = disp_height;
            }
        }
        
        // 1. 裁剪并保存彩色图像
        cv::Mat cropped_color = color(crop_roi_).clone();
        cv::imwrite("pc_color_cropped.jpg", cropped_color);
        std::cout << "  裁剪后彩色图尺寸: " << cropped_color.cols << "x" << cropped_color.rows << std::endl;
        
        // 2. 相机内参
        float cx = orig_width / 2.0f;  // 原始图像中心x坐标
        float cy = orig_height / 2.0f; // 原始图像中心y坐标
        
        // 如果焦距未指定，使用合理默认值
        if (focal_length < 0) {
            focal_length = static_cast<float>(orig_width);  // 通常接近图像宽度
        }
        
        std::cout << "相机参数:" << std::endl;
        std::cout << "  图像中心: (" << cx << ", " << cy << ")" << std::endl;
        std::cout << "  焦距: " << focal_length << std::endl;
        std::cout << "  基线: " << baseline << std::endl;
        
        // 创建PLY文件
        std::ofstream out(filename);
        if (!out.is_open()) {
            throw std::runtime_error("无法创建PLY文件: " + filename);
        }
        
        // 统计有效点数
        int valid_points = 0;
        
        // 计算视差缩放系数 - 从裁剪后的图像尺寸到视差图尺寸
        float disp_scale_factor = static_cast<float>(crop_roi_.width) / static_cast<float>(disp_width);
        std::cout << "  视差缩放系数: " << disp_scale_factor << " (裁剪宽度: " << crop_roi_.width 
                  << ", 视差图宽度: " << disp_width << ")" << std::endl;
        
        for (int y = 0; y < disp_height; y++) {
            for (int x = 0; x < disp_width; x++) {
                float d = disparity.at<float>(y, x);
                if (d <= 0) continue;  // 无效视差
                
                // 应用视差缩放因子 - 确保视差值与原始图像尺寸匹配
                float scaled_d = d * disp_scale_factor;
                
                // 计算深度
                float Z = baseline * focal_length / scaled_d;
                if (Z > 100.0f) continue;  // 深度过大，不可靠
                
                valid_points++;
            }
        }
        
        // 保存视差图可视化
        cv::Mat disp_vis;
        cv::normalize(disparity, disp_vis, 0, 255, cv::NORM_MINMAX);
        disp_vis.convertTo(disp_vis, CV_8U);
        cv::applyColorMap(disp_vis, disp_vis, cv::COLORMAP_JET);
        cv::imwrite("pc_disparity.jpg", disp_vis);

        // 写入PLY头部
        out << "ply\n";
        out << "format ascii 1.0\n";
        out << "element vertex " << valid_points << "\n";
        out << "property float x\n";
        out << "property float y\n";
        out << "property float z\n";
        out << "property uchar red\n";
        out << "property uchar green\n";
        out << "property uchar blue\n";
        out << "end_header\n";
        
        // 生成点云
        int points_written = 0;
        const int step = disp_height / 8;  // 用于调试输出的采样步长
        
        for (int y = 0; y < disp_height; y++) {
            bool is_sample_row = (y % step == 0);
            
            for (int x = 0; x < disp_width; x++) {
                float d = disparity.at<float>(y, x);
                if (d <= 0) continue;  // 无效视差
                
                // 应用视差缩放因子 - 确保视差值与原始图像尺寸匹配
                float scaled_d = d * disp_scale_factor;
                
                // 计算深度
                float Z = baseline * focal_length / scaled_d;
                if (Z > 100.0f) continue;  // 深度过大，不可靠
                
                // 重要：计算全局坐标
                // 1. 视差图上的点(x,y)对应裁剪彩色图上的同一点
                
                // 2. 将裁剪图上的点映射到原始图像坐标系
                float global_x = x + crop_roi_.x;
                float global_y = y + crop_roi_.y;
                
                // 3. 计算相对于原始图像中心的偏移并投影到3D
                float X = (global_x - cx) * Z / focal_length;
                float Y = (global_y - cy) * Z / focal_length;
                
                // 此时点(x,y)在视差图上，同时对应裁剪彩色图上的同一点
                cv::Vec3b c = cropped_color.at<cv::Vec3b>(y, x);
                
                // 调试输出
                if (is_sample_row && x % step == 0) {
                    std::cout << "采样点(" << x << "," << y << "):" << std::endl;
                    std::cout << "  原始视差值: " << d << ", 缩放后: " << scaled_d << ", 深度: " << Z << std::endl;
                    std::cout << "  全局图像坐标: (" << global_x << "," << global_y << ")" << std::endl;
                    std::cout << "  3D坐标: (" << X << "," << Y << "," << Z << ")" << std::endl;
                    std::cout << "  颜色(BGR): (" << (int)c[0] << "," << (int)c[1] << "," << (int)c[2] << ")" << std::endl;
                }
                
                // 写入点云 (注意颜色通道顺序转换：BGR -> RGB)
                out << X << " " << Y << " " << Z << " "
                    << (int)c[2] << " " << (int)c[1] << " " << (int)c[0] << "\n";
                points_written++;
            }
        }
        
        out.close();
        std::cout << "点云已保存到: " << filename << std::endl;
        std::cout << "总点数: " << points_written << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "生成点云时出错: " << e.what() << std::endl;
        throw;
    }
}

cv::Mat StereoInference::loadNpy(const std::string& filename) {
    std::ifstream file(filename, std::ios::binary);
    if (!file.is_open()) {
        throw std::runtime_error("Failed to open NPY file: " + filename);
    }
    
    // 读取头部
    char magic[6];
    file.read(magic, 6);
    if (std::string(magic, 6) != "\x93NUMPY") {
        throw std::runtime_error("Invalid NPY file format");
    }
    
    // 读取版本
    uint8_t major, minor;
    file.read(reinterpret_cast<char*>(&major), 1);
    file.read(reinterpret_cast<char*>(&minor), 1);
    
    // 读取头部长度
    uint16_t header_len;
    file.read(reinterpret_cast<char*>(&header_len), 2);
    
    // 读取头部信息
    std::vector<char> header(header_len);
    file.read(header.data(), header_len);
    std::string header_str(header.begin(), header.end());
    
    std::cout << "NPY version: " << (int)major << "." << (int)minor << std::endl;
    std::cout << "Header length: " << header_len << std::endl;
    std::cout << "Header: " << header_str << std::endl;
    
    // 解析shape信息
    size_t shape_start = header_str.find("'shape': (") + 9;
    size_t shape_end = header_str.find(")", shape_start);
    if (shape_start == std::string::npos || shape_end == std::string::npos) {
        throw std::runtime_error("Cannot find shape information in NPY header");
    }
    
    std::string shape_str = header_str.substr(shape_start, shape_end - shape_start);
    std::cout << "Shape string: " << shape_str << std::endl;
    
    // 解析维度
    std::vector<int> dims;
    std::string num_str;
    for (char c : shape_str) {
        if (std::isdigit(c)) {
            num_str += c;
        } else if (!num_str.empty()) {
            dims.push_back(std::stoi(num_str));
            num_str.clear();
        }
    }
    if (!num_str.empty()) {
        dims.push_back(std::stoi(num_str));
    }
    
    if (dims.size() != 2) {
        throw std::runtime_error("Expected 2D array, got " + std::to_string(dims.size()) + "D");
    }
    
    std::cout << "Array dimensions: " << dims[0] << " x " << dims[1] << std::endl;
    
    // 读取数据
    cv::Mat data(dims[0], dims[1], CV_32F);
    file.read(reinterpret_cast<char*>(data.data), dims[0] * dims[1] * sizeof(float));
    
    // 调整大小以匹配当前视差图
    if (data.size() != cv::Size(width_, height_)) {
        cv::Mat resized;
        cv::resize(data, resized, cv::Size(width_, height_));
        return resized;
    }
    
    return data;
}

void StereoInference::compareWithReference(const cv::Mat& disparity, 
                                         const std::string& ref_npy,
                                         const std::string& output_dir) {
    try {
        // 加载参考视差图
        cv::Mat ref_disp = loadNpy(ref_npy);
        
        if (disparity.size() != ref_disp.size()) {
            std::cout << "Resizing reference disparity map from " 
                     << ref_disp.size() << " to " << disparity.size() << std::endl;
            cv::resize(ref_disp, ref_disp, disparity.size(), 0, 0, cv::INTER_LINEAR);
        }
        
        // 计算差异
        cv::Mat diff;
        cv::absdiff(disparity, ref_disp, diff);
        
        // 计算统计信息
        double min_diff, max_diff;
        cv::Point min_loc, max_loc;
        cv::minMaxLoc(diff, &min_diff, &max_diff, &min_loc, &max_loc);
        
        cv::Scalar mean_diff, std_diff;
        cv::meanStdDev(diff, mean_diff, std_diff);
        
        // 计算相对误差
        cv::Mat rel_diff;
        cv::divide(diff, ref_disp + 1e-6, rel_diff);
        cv::Scalar mean_rel_diff = cv::mean(rel_diff);
        
        // 计算3像素误差率
        cv::Mat error_mask = diff > 3.0;
        int total_pixels = disparity.rows * disparity.cols;
        int error_pixels = cv::countNonZero(error_mask);
        double error_rate = 100.0 * error_pixels / total_pixels;
        
        // 输出统计信息
        std::cout << "\nDisparity Comparison Statistics:" << std::endl;
        std::cout << "Min absolute difference: " << min_diff << std::endl;
        std::cout << "Max absolute difference: " << max_diff << std::endl;
        std::cout << "Mean absolute difference: " << mean_diff[0] << std::endl;
        std::cout << "Std absolute difference: " << std_diff[0] << std::endl;
        std::cout << "Mean relative difference: " << mean_rel_diff[0] * 100 << "%" << std::endl;
        std::cout << "3-pixel error rate: " << error_rate << "%" << std::endl;
        
        // 保存差异可视化
        cv::Mat diff_vis;
        cv::normalize(diff, diff_vis, 0, 255, cv::NORM_MINMAX);
        diff_vis.convertTo(diff_vis, CV_8U);
        
        cv::Mat diff_color;
        cv::applyColorMap(diff_vis, diff_color, cv::COLORMAP_JET);
        
        std::string diff_path = output_dir + "/difference.png";
        cv::imwrite(diff_path, diff_color);
        
        // 创建对比图
        cv::Mat curr_vis, ref_vis;
        cv::normalize(disparity, curr_vis, 0, 255, cv::NORM_MINMAX);
        cv::normalize(ref_disp, ref_vis, 0, 255, cv::NORM_MINMAX);
        curr_vis.convertTo(curr_vis, CV_8U);
        ref_vis.convertTo(ref_vis, CV_8U);
        
        cv::Mat curr_color, ref_color;
        cv::applyColorMap(curr_vis, curr_color, cv::COLORMAP_JET);
        cv::applyColorMap(ref_vis, ref_color, cv::COLORMAP_JET);
        
        // 创建误差掩码可视化
        cv::Mat error_vis;
        error_mask.convertTo(error_vis, CV_8U, 255);
        cv::Mat error_color;
        cv::cvtColor(error_vis, error_color, cv::COLOR_GRAY2BGR);
        error_color = cv::Scalar(0, 0, 255);
        cv::bitwise_and(curr_color, error_color, error_color, error_mask);
        cv::addWeighted(curr_color, 0.7, error_color, 0.3, 0, error_color);
        
        cv::Mat comparison;
        cv::hconcat(std::vector<cv::Mat>{curr_color, ref_color, diff_color, error_color}, comparison);
        
        // 添加文字标签
        int font = cv::FONT_HERSHEY_SIMPLEX;
        double font_scale = 1.0;
        int thickness = 2;
        cv::Scalar text_color(255, 255, 255);
        
        cv::putText(comparison, "Current", cv::Point(curr_color.cols/2 - 50, 30),
                    font, font_scale, text_color, thickness);
        cv::putText(comparison, "Reference", cv::Point(curr_color.cols + ref_color.cols/2 - 50, 30),
                    font, font_scale, text_color, thickness);
        cv::putText(comparison, "Difference", cv::Point(2*curr_color.cols + diff_color.cols/2 - 50, 30),
                    font, font_scale, text_color, thickness);
        cv::putText(comparison, "Error > 3px", cv::Point(3*curr_color.cols + error_color.cols/2 - 50, 30),
                    font, font_scale, text_color, thickness);
        
        std::string comp_path = output_dir + "/comparison.png";
        cv::imwrite(comp_path, comparison);
        
        std::cout << "Comparison visualization saved to: " << comp_path << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Error in compareWithReference: " << e.what() << std::endl;
        throw;
    }
}

/**
 * 体素网格滤波 - 均匀降采样
 */
pcl::PointCloud<pcl::PointXYZRGB>::Ptr StereoInference::voxelGridFilter(
    const pcl::PointCloud<pcl::PointXYZRGB>::Ptr& cloud,
    float leaf_size) {
    
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr filtered_cloud(new pcl::PointCloud<pcl::PointXYZRGB>);
    
    // 创建体素滤波器对象
    pcl::VoxelGrid<pcl::PointXYZRGB> voxel_filter;
    voxel_filter.setInputCloud(cloud);
    voxel_filter.setLeafSize(leaf_size, leaf_size, leaf_size);
    voxel_filter.filter(*filtered_cloud);
    
    return filtered_cloud;
}

/**
 * 统计离群点滤波 - 移除噪声
 */
pcl::PointCloud<pcl::PointXYZRGB>::Ptr StereoInference::statisticalOutlierRemoval(
    const pcl::PointCloud<pcl::PointXYZRGB>::Ptr& cloud,
    int mean_k,
    float std_dev_mul) {
    
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr filtered_cloud(new pcl::PointCloud<pcl::PointXYZRGB>);
    
    // 创建统计滤波器对象
    pcl::StatisticalOutlierRemoval<pcl::PointXYZRGB> sor;
    sor.setInputCloud(cloud);
    sor.setMeanK(mean_k);  // 分析邻近点的数量
    sor.setStddevMulThresh(std_dev_mul);  // 标准差阈值
    sor.filter(*filtered_cloud);
    
    return filtered_cloud;
}

/**
 * 半径离群点滤波 - 移除孤立点
 */
pcl::PointCloud<pcl::PointXYZRGB>::Ptr StereoInference::radiusOutlierRemoval(
    const pcl::PointCloud<pcl::PointXYZRGB>::Ptr& cloud,
    float radius,
    int min_neighbors) {
    
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr filtered_cloud(new pcl::PointCloud<pcl::PointXYZRGB>);
    
    // 创建半径滤波器对象
    pcl::RadiusOutlierRemoval<pcl::PointXYZRGB> ror;
    ror.setInputCloud(cloud);
    ror.setRadiusSearch(radius);  // 搜索半径
    ror.setMinNeighborsInRadius(min_neighbors);  // 最小邻居数量
    ror.filter(*filtered_cloud);
    
    return filtered_cloud;
}

/**
 * 深度范围滤波 - 移除距离太近或太远的点
 */
pcl::PointCloud<pcl::PointXYZRGB>::Ptr StereoInference::depthRangeFilter(
    const pcl::PointCloud<pcl::PointXYZRGB>::Ptr& cloud,
    float min_depth,
    float max_depth) {
    
    pcl::console::TicToc timer;
    timer.tic();
    
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr filtered_cloud(new pcl::PointCloud<pcl::PointXYZRGB>);
    filtered_cloud->reserve(cloud->size());  // 预先分配内存以提高性能
    
    // 遍历所有点，只保留指定深度范围内的点
    for (const auto& point : cloud->points) {
        float depth = std::sqrt(point.x * point.x + point.y * point.y + point.z * point.z);
        
        if (depth >= min_depth && depth <= max_depth) {
            filtered_cloud->push_back(point);
        }
    }
    
    // 确保点云数据字段设置正确
    filtered_cloud->width = filtered_cloud->size();
    filtered_cloud->height = 1;
    filtered_cloud->is_dense = false;
    
    std::cout << "深度范围滤波 [" << min_depth << "m, " << max_depth << "m]: " 
              << cloud->size() << " -> " << filtered_cloud->size() 
              << " 点 (减少了 " << (100.0f * (cloud->size() - filtered_cloud->size()) / cloud->size()) 
              << "%), 用时: " << timer.toc() << " ms" << std::endl;
    
    return filtered_cloud;
}

/**
 * 组合过滤 - 依次应用多种过滤方法
 */
pcl::PointCloud<pcl::PointXYZRGB>::Ptr StereoInference::filterPointCloud(
    const pcl::PointCloud<pcl::PointXYZRGB>::Ptr& cloud,
    const PointCloudFilterParams& params) {
    
    // 1. 首先应用深度范围滤波，去除远近点
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr depth_filtered = 
        depthRangeFilter(cloud, params.min_depth, params.max_depth);
    
    // 如果过滤后点云太小，就返回深度过滤后的结果
    if (depth_filtered->size() < 100) {
        return depth_filtered;
    }
    
    // 2. 应用统计离群点滤波，去除噪声
    // 使用更加严格的参数：更多的邻域点数和更小的标准差倍数
    int adjusted_mean_k = std::max(50, params.mean_k); // 增加到至少50个邻域点
    float adjusted_std_dev = std::min(1.0f, params.std_dev_mul); // 降低到最大1.0倍标准差
    
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr stat_filtered = 
        statisticalOutlierRemoval(depth_filtered, adjusted_mean_k, adjusted_std_dev);
    
    // 如果过滤后点云太小，就返回统计过滤后的结果
    if (stat_filtered->size() < 100) {
        return stat_filtered;
    }
    
    // 3. 计算点云密度，自适应调整半径
    float avg_spacing = 0.0f;
    // 采样计算平均距离
    int sample_size = std::min(1000, (int)stat_filtered->size());
    int sample_step = std::max(1, (int)(stat_filtered->size() / sample_size));
    
    std::vector<float> distances;
    for (size_t i = 0; i < stat_filtered->points.size(); i += sample_step) {
        const auto& p1 = stat_filtered->points[i];
        
        // 找最近的5个点
        std::vector<float> point_distances;
        for (size_t j = 0; j < stat_filtered->points.size(); j += sample_step) {
            if (i == j) continue;
            
            const auto& p2 = stat_filtered->points[j];
            float distance = std::sqrt(
                (p1.x - p2.x) * (p1.x - p2.x) +
                (p1.y - p2.y) * (p1.y - p2.y) +
                (p1.z - p2.z) * (p1.z - p2.z)
            );
            
            point_distances.push_back(distance);
            
            if (point_distances.size() >= 5) break;
        }
        
        if (!point_distances.empty()) {
            std::sort(point_distances.begin(), point_distances.end());
            distances.push_back(point_distances[0]);  // 最近的点的距离
        }
    }
    
    if (!distances.empty()) {
        avg_spacing = std::accumulate(distances.begin(), distances.end(), 0.0f) / distances.size();
    } else {
        avg_spacing = 0.05f;  // 默认值
    }
    
    // 计算建议的半径 - 应该大于点的平均间距
    float suggested_radius = avg_spacing * 5.0f; // 增加倍数，从3倍改为5倍
    
    // 安全检查 - 确保半径在合理范围内
    if (suggested_radius < 0.02f) suggested_radius = 0.02f; // 增加最小值
    if (suggested_radius > 0.5f) suggested_radius = 0.5f;
    
    // 使用自适应半径和增加邻居数量
    float adaptive_radius = std::max(params.radius, suggested_radius);
    int adaptive_min_neighbors = std::max(15, params.min_neighbors); // 增加最小邻居数量从3到15
    
    // 应用半径滤波，去除孤立点
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr radius_filtered = 
        radiusOutlierRemoval(stat_filtered, adaptive_radius, adaptive_min_neighbors);
    
    // 如果过滤后点云太小，就返回半径过滤前的结果
    if (radius_filtered->size() < 100) {
        radius_filtered = stat_filtered;
    }
    
    // 4. 最后应用体素网格滤波，进行均匀降采样
    float adjusted_voxel_size = std::max(0.01f, params.voxel_size); // 确保最小体素大小
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr voxel_filtered = 
        voxelGridFilter(radius_filtered, adjusted_voxel_size);
    
    return voxel_filtered;
}

/**
 * 直接将过滤应用到PLY文件
 */
bool StereoInference::filterPointCloudFile(
    const std::string& input_file,
    const std::string& output_file,
    const PointCloudFilterParams& params) {
    
    // 读取输入点云
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZRGB>);
    if (pcl::io::loadPLYFile<pcl::PointXYZRGB>(input_file, *cloud) == -1) {
        std::cerr << "无法读取文件: " << input_file << std::endl;
        return false;
    }
    
    // 应用过滤
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr filtered_cloud = filterPointCloud(cloud, params);
    
    // 保存结果
    if (pcl::io::savePLYFile(output_file, *filtered_cloud) == -1) {
        std::cerr << "无法保存文件: " << output_file << std::endl;
        return false;
    }
    
    std::cout << "已保存过滤后的点云到: " << output_file 
              << " (" << filtered_cloud->size() << " 点)" << std::endl;
    
    return true;
}

/**
 * 平面拟合结果结构体
 */
struct PlaneResult {
    pcl::ModelCoefficients::Ptr coefficients;
    pcl::PointIndices::Ptr inliers;
    Eigen::Vector3f normal;
    double distance;
};

/**
 * 使用RANSAC算法拟合平面
 */
PlaneResult fitPlaneRANSAC(const pcl::PointCloud<pcl::PointXYZRGB>::Ptr& cloud, 
                          double distance_threshold = 0.02, 
                          int max_iterations = 1000) {
    pcl::console::TicToc timer;
    timer.tic();
    
    std::cout << "开始平面拟合: 点云大小 = " << cloud->points.size() << " 点" << std::endl;
    std::cout << "RANSAC参数: 距离阈值 = " << distance_threshold << ", 最大迭代次数 = " << max_iterations << std::endl;
    
    // 初始化结果
    PlaneResult result;
    result.coefficients.reset(new pcl::ModelCoefficients);
    result.inliers.reset(new pcl::PointIndices);
    
    // 创建分割器对象
    pcl::SACSegmentation<pcl::PointXYZRGB> seg;
    seg.setOptimizeCoefficients(true); // 优化系数
    seg.setModelType(pcl::SACMODEL_PLANE); // 模型类型：平面
    seg.setMethodType(pcl::SAC_RANSAC); // 方法：RANSAC
    seg.setDistanceThreshold(distance_threshold); // 距离阈值
    seg.setMaxIterations(max_iterations); // 最大迭代次数
    seg.setInputCloud(cloud);
    
    // 执行分割
    seg.segment(*(result.inliers), *(result.coefficients));
    
    if (result.inliers->indices.size() == 0) {
        std::cerr << "无法找到符合模型的平面" << std::endl;
    } else {
        // 计算平面法向量
        result.normal = Eigen::Vector3f(
            result.coefficients->values[0],
            result.coefficients->values[1],
            result.coefficients->values[2]
        );
        // 已经在SACSegmentation中进行了归一化，不需要再次归一化
        
        // 计算平面距离
        result.distance = result.coefficients->values[3];
        
        std::cout << "平面拟合完成，用时: " << timer.toc() << " ms" << std::endl;
        std::cout << "平面方程: " << result.coefficients->values[0] << " x + "
                  << result.coefficients->values[1] << " y + "
                  << result.coefficients->values[2] << " z + "
                  << result.coefficients->values[3] << " = 0" << std::endl;
        std::cout << "法向量: (" << result.normal[0] << ", " 
                  << result.normal[1] << ", " << result.normal[2] << ")" << std::endl;
        std::cout << "内点数量: " << result.inliers->indices.size() 
                  << " 占总点数的 " 
                  << (100.0 * result.inliers->indices.size() / cloud->points.size()) << "%" << std::endl;
    }
    
    return result;
}

/**
 * 根据平面法向量计算变换矩阵，将平面法向量对齐到特定方向
 */
Eigen::Matrix4f computeTransformationMatrix(const Eigen::Vector3f& plane_normal, 
                                           const Eigen::Vector3f& target_normal = Eigen::Vector3f(0, 0, 1)) {
    // 创建局部副本并归一化
    Eigen::Vector3f plane_normal_normalized = plane_normal.normalized();
    Eigen::Vector3f target_normal_normalized = target_normal.normalized();
    
    // 计算旋转轴和旋转角度
    Eigen::Vector3f rotation_axis = plane_normal_normalized.cross(target_normal_normalized);
    
    if (rotation_axis.norm() < 1e-6) {
        // 向量平行或反平行
        if (plane_normal_normalized.dot(target_normal_normalized) > 0) {
            // 向量已对齐，返回单位矩阵
            return Eigen::Matrix4f::Identity();
        } else {
            // 向量反平行，旋转180度
            Eigen::Matrix4f transform = Eigen::Matrix4f::Identity();
            transform.block<3,3>(0,0) = -Eigen::Matrix3f::Identity(); // 绕任意轴旋转180度
            return transform;
        }
    }
    
    rotation_axis.normalize();
    float angle = acos(plane_normal_normalized.dot(target_normal_normalized));
    
    // 使用罗德里格斯公式构建旋转矩阵
    Eigen::Matrix3f rotation = Eigen::AngleAxisf(angle, rotation_axis).toRotationMatrix();
    
    // 构建4x4变换矩阵
    Eigen::Matrix4f transformation = Eigen::Matrix4f::Identity();
    transformation.block<3,3>(0,0) = rotation;
    
    return transformation;
}

/**
 * 优化点云：拟合平面并将其对齐到指定方向
 */
pcl::PointCloud<pcl::PointXYZRGB>::Ptr optimizePointCloud(
    const pcl::PointCloud<pcl::PointXYZRGB>::Ptr& input_cloud, 
    double distance_threshold = 0.02,
    bool project_to_plane = false,
    int max_iterations = 2000) {
    
    pcl::console::TicToc timer;
    timer.tic();
    
    // 1. 使用RANSAC拟合平面
    PlaneResult plane = fitPlaneRANSAC(input_cloud, distance_threshold, max_iterations);
    
    if (plane.inliers->indices.empty()) {
        std::cerr << "平面拟合失败，无法优化点云" << std::endl;
        return input_cloud;
    }
    
    // 2. 计算变换矩阵，使平面法向量朝向z轴正方向
    Eigen::Matrix4f transform = computeTransformationMatrix(plane.normal);
    
    // 3. 应用变换
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr transformed_cloud(new pcl::PointCloud<pcl::PointXYZRGB>);
    pcl::transformPointCloud(*input_cloud, *transformed_cloud, transform);
    
    // 4. 计算点云Z轴平面方程
    pcl::ModelCoefficients::Ptr plane_coeffs(new pcl::ModelCoefficients());
    plane_coeffs->values.resize(4);
    plane_coeffs->values[0] = 0;
    plane_coeffs->values[1] = 0;
    plane_coeffs->values[2] = 1;
    
    // 找到转换后点云中平面点的最小Z值
    float min_z = std::numeric_limits<float>::max();
    for (const auto& index : plane.inliers->indices) {
        const auto& point = transformed_cloud->points[index];
        if (point.z < min_z) {
            min_z = point.z;
        }
    }
    
    // 设置平面方程的D值为最小Z值
    plane_coeffs->values[3] = -min_z;
    
    std::cout << "转换后的平面Z值: " << min_z << std::endl;
    
    // 5. 使所有点的Z坐标相对于拟合平面的最小Z值
    for (auto& point : transformed_cloud->points) {
        point.z -= min_z; // 平移使平面的最低点位于Z=0
    }
    
    // 可选：将点投影到平面上
    if (project_to_plane) {
        // 提取平面内点
        pcl::PointCloud<pcl::PointXYZRGB>::Ptr plane_cloud(new pcl::PointCloud<pcl::PointXYZRGB>);
        pcl::ExtractIndices<pcl::PointXYZRGB> extract;
        extract.setInputCloud(transformed_cloud);
        extract.setIndices(plane.inliers);
        extract.setNegative(false); // 提取内点
        extract.filter(*plane_cloud);
        
        // 将平面内点投影到平面上
        pcl::PointCloud<pcl::PointXYZRGB>::Ptr projected_cloud(new pcl::PointCloud<pcl::PointXYZRGB>);
        pcl::ProjectInliers<pcl::PointXYZRGB> proj;
        proj.setModelType(pcl::SACMODEL_PLANE);
        proj.setInputCloud(plane_cloud);
        proj.setModelCoefficients(plane_coeffs);
        proj.filter(*projected_cloud);
        
        // 提取非平面点
        pcl::PointCloud<pcl::PointXYZRGB>::Ptr non_plane_cloud(new pcl::PointCloud<pcl::PointXYZRGB>);
        extract.setNegative(true); // 提取外点
        extract.filter(*non_plane_cloud);
        
        // 合并投影平面点和非平面点
        *projected_cloud += *non_plane_cloud;
        
        std::cout << "点云优化完成 (包含投影)，总用时: " << timer.toc() << " ms" << std::endl;
        return projected_cloud;
    }
    
    std::cout << "点云优化完成，总用时: " << timer.toc() << " ms" << std::endl;
    return transformed_cloud;
}

/**
 * 保存优化后的点云
 */
bool StereoInference::saveOptimizedPointCloud(
    const std::string& input_file,
    const std::string& output_file,
    double distance_threshold,
    bool project_to_plane) {
    
    // 读取输入点云
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZRGB>);
    if (pcl::io::loadPLYFile<pcl::PointXYZRGB>(input_file, *cloud) == -1) {
        std::cerr << "无法读取文件: " << input_file << std::endl;
        return false;
    }
    
    // 检查是否需要先应用一些预处理过滤提高平面拟合质量
    if (cloud->size() > 50000) {  // 如果点云过大，先做一次降采样
        std::cout << "点云较大，进行预处理降采样..." << std::endl;
        float preprocess_voxel_size = 0.03f;  // 预处理使用较大体素
        cloud = voxelGridFilter(cloud, preprocess_voxel_size);
    }
    
    // 应用初步统计滤波去除噪点，提高平面拟合质量
    if (cloud->size() > 1000) {
        std::cout << "应用初步噪点过滤以提高平面拟合质量..." << std::endl;
        cloud = statisticalOutlierRemoval(cloud, 30, 1.0);
    }
    
    // 设置更严格的平面拟合参数
    double adjusted_distance_threshold = std::min(0.03, distance_threshold);
    int max_iterations = 2000;  // 增加RANSAC最大迭代次数
    
    // 应用优化
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr optimized_cloud = 
        optimizePointCloud(cloud, adjusted_distance_threshold, project_to_plane, max_iterations);
    
    // 保存结果
    if (pcl::io::savePLYFile(output_file, *optimized_cloud) == -1) {
        std::cerr << "无法保存文件: " << output_file << std::endl;
        return false;
    }
    
    std::cout << "最终结果保存到: " << output_file << std::endl;
    return true;
} 