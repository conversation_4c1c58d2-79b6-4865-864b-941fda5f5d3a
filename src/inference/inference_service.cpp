#include "inference/inference_service.hpp"
#include "infrastructure/logging/logger.h"

namespace SmartScope {

InferenceService& InferenceService::instance() {
    static InferenceService instance;
    return instance;
}

InferenceService::InferenceService()
    : m_running(false)
    , m_initialized(false)
    , m_currentSessionId(QDateTime::currentMSecsSinceEpoch()) // 使用当前时间戳初始化会话ID
{
    // 将对象移动到工作线程
    this->moveToThread(&m_workerThread);
    
    // 连接信号
    connect(this, &InferenceService::newRequestAvailable,
            this, &InferenceService::processRequest,
            Qt::QueuedConnection);
            
    // 启动工作线程
    m_workerThread.start();
}

InferenceService::~InferenceService() {
    stop();
}

bool InferenceService::initialize(const QString& model_path) {
    QMutexLocker locker(&m_mutex);
    
    if (m_initialized) {
        logInfo("推理服务已经初始化");
        return true;
    }
    
    try {
        // 创建推理对象
        m_inference = std::make_unique<StereoInference>(model_path.toStdString());
        
        // 设置为ULTRA_FAST模式作为默认值(256×256分辨率)
        m_inference->setPerformanceMode(StereoInference::ULTRA_FAST);
        
        m_initialized = true;
        m_running = true;
        
        logInfo("推理服务初始化成功 (使用极速模式: 256×256)");
        return true;
    } catch (const std::exception& e) {
        logError(QString("推理服务初始化失败: %1").arg(e.what()));
        return false;
    }
}

void InferenceService::setPerformanceMode(StereoInference::PerformanceMode mode) {
    QMutexLocker locker(&m_mutex);
    
    if (!m_initialized) {
        logError("推理服务未初始化，无法设置性能模式");
        return;
    }
    
    try {
        m_inference->setPerformanceMode(mode);
        QString mode_str;
        switch (mode) {
            case StereoInference::HIGH_QUALITY: mode_str = "高质量"; break;
            case StereoInference::BALANCED: mode_str = "平衡"; break;
            case StereoInference::FAST: mode_str = "快速"; break;
            case StereoInference::ULTRA_FAST: mode_str = "极速"; break;
            default: mode_str = "未知";
        }
        logInfo(QString("性能模式设置为: %1").arg(mode_str));
    } catch (const std::exception& e) {
        logError(QString("设置性能模式失败: %1").arg(e.what()));
    }
}

StereoInference::PerformanceMode InferenceService::getPerformanceMode() const {
    QMutexLocker locker(&m_mutex);
    
    if (!m_initialized) {
        logError("推理服务未初始化，无法获取性能模式");
        return StereoInference::BALANCED; // 默认返回平衡模式
    }
    
    return m_inference->getPerformanceMode();
}

void InferenceService::submitRequest(const InferenceRequest& request) {
    QMutexLocker locker(&m_mutex);
    
    if (!m_initialized) {
        logError("推理服务未初始化");
        InferenceResult result;
        result.success = false;
        result.error_message = "推理服务未初始化";
        result.session_id = m_currentSessionId; // 添加当前会话ID
        emit inferenceCompleted(result);
        return;
    }
    
    // 创建请求副本并设置会话ID
    InferenceRequest requestWithSessionId = request;
    requestWithSessionId.session_id = m_currentSessionId;
    
    // 添加请求到队列
    m_requestQueue.enqueue(requestWithSessionId);
    
    logInfo(QString("推理请求已加入队列，会话ID: %1").arg(m_currentSessionId));
    
    // 通知处理线程
    emit newRequestAvailable();
}

void InferenceService::cancelCurrentTask() {
    QMutexLocker locker(&m_mutex);
    
    // 清空请求队列
    if (!m_requestQueue.isEmpty()) {
        logInfo("取消当前推理任务，清空请求队列");
        m_requestQueue.clear();
    }
    
    // 构造一个取消的结果通知
    InferenceResult result;
    result.success = false;
    result.error_message = "推理任务被用户取消";
    result.session_id = m_currentSessionId; // 添加当前会话ID
    
    // 发送取消通知
    locker.unlock(); // 解锁以避免死锁
    emit inferenceCompleted(result);
    
    logInfo("推理任务已取消");
}

void InferenceService::processRequest() {
    QMutexLocker locker(&m_mutex);
    
    while (!m_requestQueue.isEmpty() && m_running) {
        // 获取请求
        InferenceRequest request = m_requestQueue.dequeue();
        locker.unlock();
        
        InferenceResult result;
        result.save_path = request.save_path;
        // 传递原始图像尺寸
        result.original_width = request.original_width;
        result.original_height = request.original_height;
        // 传递会话ID
        result.session_id = request.session_id;
        
        try {
            // 记录开始时间
            auto total_start_time = std::chrono::high_resolution_clock::now();
            
            // 首先对图像进行预处理裁剪
            cv::Mat cropped_left = m_inference->preprocessCropFixed(request.left_image);
            cv::Mat cropped_right = m_inference->preprocessCropFixed(request.right_image);
            
            // 执行推理
            result.depth_map = m_inference->inference(cropped_left, cropped_right);
            
            // 记录推理结束时间
            auto inference_end_time = std::chrono::high_resolution_clock::now();
            auto inference_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                inference_end_time - total_start_time).count();
            
            // 保存结果
            if (!request.save_path.isEmpty()) {
                auto save_start_time = std::chrono::high_resolution_clock::now();
                
                m_inference->saveDisparity(result.depth_map, request.save_path.toStdString());
                
                // 如果需要生成点云
                if (request.generate_pointcloud) {
                    // 生成原始点云
                    QString pointcloud_path = request.save_path;
                    pointcloud_path.replace(".png", ".ply");
                    result.pointcloud_path = pointcloud_path;
                    
                    m_inference->savePointCloud(result.depth_map, request.left_image,
                                             pointcloud_path.toStdString(),
                                             request.baseline,
                                             request.focal_length);
                    
                    // 应用点云过滤（如果启用）
                    if (request.apply_filter) {
                        auto filter_start_time = std::chrono::high_resolution_clock::now();
                        
                        QString filtered_pointcloud_path = request.save_path;
                        filtered_pointcloud_path.replace(".png", "_filtered.ply");
                        result.filtered_pointcloud_path = filtered_pointcloud_path;
                        
                        try {
                            result.filter_success = m_inference->filterPointCloudFile(
                                pointcloud_path.toStdString(),
                                filtered_pointcloud_path.toStdString(),
                                request.filter_params);
                                
                            auto filter_end_time = std::chrono::high_resolution_clock::now();
                            auto filter_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                                filter_end_time - filter_start_time).count();
                                
                            logInfo(QString("点云过滤耗时: %1 ms").arg(filter_duration));
                            
                            // 应用点云优化（如果启用），优化应用在过滤后的点云上
                            if (request.apply_optimize && result.filter_success) {
                                auto optimize_start_time = std::chrono::high_resolution_clock::now();
                                
                                QString optimized_pointcloud_path = request.save_path;
                                optimized_pointcloud_path.replace(".png", "_optimized.ply");
                                result.optimized_pointcloud_path = optimized_pointcloud_path;
                                
                                try {
                                    result.optimize_success = m_inference->saveOptimizedPointCloud(
                                        filtered_pointcloud_path.toStdString(),
                                        optimized_pointcloud_path.toStdString(),
                                        request.optimize_threshold,
                                        request.project_to_plane);
                                        
                                    auto optimize_end_time = std::chrono::high_resolution_clock::now();
                                    auto optimize_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                                        optimize_end_time - optimize_start_time).count();
                                        
                                    logInfo(QString("点云优化耗时: %1 ms").arg(optimize_duration));
                                } catch (const std::exception& e) {
                                    logError(QString("点云优化失败: %1").arg(e.what()));
                                    result.optimize_success = false;
                                }
                            }
                        } catch (const std::exception& e) {
                            logError(QString("点云过滤失败: %1").arg(e.what()));
                            result.filter_success = false;
                            
                            // 如果过滤失败但需要优化，直接在原始点云上应用优化
                            if (request.apply_optimize) {
                                auto optimize_start_time = std::chrono::high_resolution_clock::now();
                                
                                QString optimized_pointcloud_path = request.save_path;
                                optimized_pointcloud_path.replace(".png", "_optimized.ply");
                                result.optimized_pointcloud_path = optimized_pointcloud_path;
                                
                                try {
                                    result.optimize_success = m_inference->saveOptimizedPointCloud(
                                        pointcloud_path.toStdString(),
                                        optimized_pointcloud_path.toStdString(),
                                        request.optimize_threshold,
                                        request.project_to_plane);
                                        
                                    auto optimize_end_time = std::chrono::high_resolution_clock::now();
                                    auto optimize_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                                        optimize_end_time - optimize_start_time).count();
                                        
                                    logInfo(QString("点云优化耗时: %1 ms").arg(optimize_duration));
                                } catch (const std::exception& e) {
                                    logError(QString("点云优化失败: %1").arg(e.what()));
                                    result.optimize_success = false;
                                }
                            }
                        }
                    }
                    // 如果没有启用过滤但需要优化，直接在原始点云上应用优化
                    else if (request.apply_optimize) {
                        auto optimize_start_time = std::chrono::high_resolution_clock::now();
                        
                        QString optimized_pointcloud_path = request.save_path;
                        optimized_pointcloud_path.replace(".png", "_optimized.ply");
                        result.optimized_pointcloud_path = optimized_pointcloud_path;
                        
                        try {
                            result.optimize_success = m_inference->saveOptimizedPointCloud(
                                pointcloud_path.toStdString(),
                                optimized_pointcloud_path.toStdString(),
                                request.optimize_threshold,
                                request.project_to_plane);
                                
                            auto optimize_end_time = std::chrono::high_resolution_clock::now();
                            auto optimize_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                                optimize_end_time - optimize_start_time).count();
                                
                            logInfo(QString("点云优化耗时: %1 ms").arg(optimize_duration));
                        } catch (const std::exception& e) {
                            logError(QString("点云优化失败: %1").arg(e.what()));
                            result.optimize_success = false;
                        }
                    }
                }
                
                auto save_end_time = std::chrono::high_resolution_clock::now();
                auto save_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                    save_end_time - save_start_time).count();
                
                logInfo(QString("保存结果耗时: %1 ms").arg(save_duration));
            }
            
            // 记录总结束时间
            auto total_end_time = std::chrono::high_resolution_clock::now();
            auto total_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                total_end_time - total_start_time).count();
            
            result.success = true;
            
            // 输出详细的时间信息
            logInfo(QString("推理完成 - 纯推理耗时: %1 ms, 总耗时: %2 ms, 图像大小: %3x%4")
                   .arg(inference_duration)
                   .arg(total_duration)
                   .arg(request.left_image.cols)
                   .arg(request.left_image.rows));
            
        } catch (const std::exception& e) {
            result.success = false;
            result.error_message = QString("推理失败: %1").arg(e.what());
            logError(result.error_message);
        }
        
        // 发送结果
        logInfo(QString("发送推理结果 - 深度图是否为空: %1, 尺寸: %2x%3")
                .arg(result.depth_map.empty() ? "是" : "否")
                .arg(result.depth_map.empty() ? 0 : result.depth_map.cols)
                .arg(result.depth_map.empty() ? 0 : result.depth_map.rows));
        logInfo("开始发射inferenceCompleted信号...");
        emit inferenceCompleted(result);
        logInfo("inferenceCompleted信号已发射完成");
        
        locker.relock();
    }
}

void InferenceService::stop() {
    {
        QMutexLocker locker(&m_mutex);
        if (!m_running) return;
        
        m_running = false;
        m_initialized = false;
        m_requestQueue.clear();
    }
    
    // 停止工作线程
    m_workerThread.quit();
    m_workerThread.wait();
    
    // 清理资源
    m_inference.reset();
    
    logInfo("推理服务已停止");
}

bool InferenceService::isRunning() const {
    QMutexLocker locker(&m_mutex);
    return m_running;
}

bool InferenceService::isInitialized() const {
    QMutexLocker locker(&m_mutex);
    return m_initialized;
}

qint64 InferenceService::getCurrentSessionId() const {
    QMutexLocker locker(&m_mutex);
    return m_currentSessionId;
}

StereoInference* InferenceService::getInferencePtr() const {
    QMutexLocker locker(&m_mutex);
    return m_inference.get();
}

void InferenceService::logInfo(const QString& message) const {
    LOG_INFO(message);
}

void InferenceService::logError(const QString& message) const {
    LOG_ERROR(message);
}

void InferenceService::resetService() {
    QMutexLocker locker(&m_mutex);
    
    // 清空请求队列
    if (!m_requestQueue.isEmpty()) {
        logInfo("重置推理服务，清空请求队列");
        m_requestQueue.clear();
    }
    
    // 重置会话ID
    qint64 newSessionId = resetSessionId();
    
    // 阻断当前正在处理的任务
    logInfo("推理服务已完全重置");
    
    locker.unlock(); // 解锁以避免死锁
    
    // 发送一个重置通知，确保任何等待结果的地方都能收到通知
    InferenceResult result;
    result.success = false;
    result.error_message = "推理服务已重置";
    result.session_id = newSessionId; // 使用新的会话ID
    emit inferenceCompleted(result);
}

qint64 InferenceService::resetSessionId() {
    QMutexLocker locker(&m_mutex);
    // 使用当前时间戳生成新的会话ID
    m_currentSessionId = QDateTime::currentMSecsSinceEpoch();
    logInfo(QString("重置会话ID: %1").arg(m_currentSessionId));
    return m_currentSessionId;
}

void InferenceService::shutdown() {
    QMutexLocker locker(&m_mutex);
    
    logInfo("正在关闭推理服务...");
    
    // 停止服务
    stop();
    
    // 释放推理引擎资源
    if (m_inference) {
        try {
            m_inference.reset();
            logInfo("推理引擎资源已释放");
        } catch (const std::exception& e) {
            logError(QString("释放推理引擎时发生异常: %1").arg(e.what()));
        }
    }
    
    // 重置状态
    m_initialized = false;
    
    logInfo("推理服务已关闭");
}

} // namespace SmartScope 