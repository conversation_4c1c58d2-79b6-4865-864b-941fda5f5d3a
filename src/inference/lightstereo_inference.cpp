#include "inference/lightstereo_inference.hpp"
#include <iostream>
#include <opencv2/opencv.hpp>

// 临时实现：模拟 LightStereo 推理
// 在实际部署时，这里应该集成真正的 RKNN 推理代码

namespace lightstereo_inference {

class LightStereoInference::Impl {
public:
    std::string model_path_;
    bool initialized_;
    cv::Size model_input_size_;

    Impl(const std::string& model_path) 
        : model_path_(model_path), initialized_(false), model_input_size_(960, 576) {}
    
    bool Initialize() {
        // 临时实现：检查模型文件是否存在
        std::cout << "Initializing LightStereo with model: " << model_path_ << std::endl;
        
        // 在实际实现中，这里应该：
        // 1. 加载 RKNN 模型
        // 2. 初始化推理引擎
        // 3. 设置输入输出张量
        
        initialized_ = true;
        std::cout << "LightStereo initialized successfully (mock implementation)" << std::endl;
        return true;
    }
    
    bool Inference(const cv::Mat& left_image, const cv::Mat& right_image, 
                   cv::Mat& disparity_map, ScaleInfo* scale_info = nullptr) {
        if (!initialized_) {
            std::cerr << "LightStereo not initialized!" << std::endl;
            return false;
        }
        
        if (left_image.empty() || right_image.empty()) {
            std::cerr << "Input images are empty!" << std::endl;
            return false;
        }
        
        if (left_image.size() != right_image.size()) {
            std::cerr << "Left and right images must have the same size!" << std::endl;
            return false;
        }
        
        // 记录缩放信息
        if (scale_info) {
            scale_info->original_size = left_image.size();
            scale_info->model_input_size = model_input_size_;
            
            float scale_x = static_cast<float>(scale_info->original_size.width) / model_input_size_.width;
            float scale_y = static_cast<float>(scale_info->original_size.height) / model_input_size_.height;
            scale_info->scale_factor = std::max(scale_x, scale_y);
        }
        
        std::cout << "Processing images: " << left_image.size() << std::endl;
        
        // 临时实现：生成模拟视差图
        // 在实际实现中，这里应该：
        // 1. 预处理图像（缩放、归一化）
        // 2. 执行 RKNN 推理
        // 3. 后处理输出结果
        
        // 创建模拟视差图（基于简单的块匹配）
        cv::Mat left_gray, right_gray;
        cv::cvtColor(left_image, left_gray, cv::COLOR_BGR2GRAY);
        cv::cvtColor(right_image, right_gray, cv::COLOR_BGR2GRAY);
        
        // 使用 OpenCV 的 StereoBM 作为临时替代
        auto stereo = cv::StereoBM::create(16, 21);
        stereo->compute(left_gray, right_gray, disparity_map);
        
        // 转换为浮点格式
        disparity_map.convertTo(disparity_map, CV_32F, 1.0/16.0);
        
        std::cout << "Mock inference completed, disparity map size: " << disparity_map.size() << std::endl;
        return true;
    }
};

LightStereoInference::LightStereoInference(const std::string& model_path)
    : pImpl(std::make_unique<Impl>(model_path)) {
}

LightStereoInference::~LightStereoInference() = default;

bool LightStereoInference::Initialize() {
    return pImpl->Initialize();
}

bool LightStereoInference::Inference(const cv::Mat& left_image, 
                                    const cv::Mat& right_image, 
                                    cv::Mat& disparity_map) {
    return pImpl->Inference(left_image, right_image, disparity_map);
}

bool LightStereoInference::InferenceWithScaleInfo(const cv::Mat& left_image, 
                                                  const cv::Mat& right_image, 
                                                  cv::Mat& disparity_map,
                                                  ScaleInfo& scale_info) {
    return pImpl->Inference(left_image, right_image, disparity_map, &scale_info);
}

bool LightStereoInference::InferenceFromFiles(const std::string& left_image_path,
                                             const std::string& right_image_path,
                                             const std::string& output_path) {
    cv::Mat left_image = cv::imread(left_image_path, cv::IMREAD_COLOR);
    cv::Mat right_image = cv::imread(right_image_path, cv::IMREAD_COLOR);

    if (left_image.empty()) {
        std::cerr << "Failed to load left image: " << left_image_path << std::endl;
        return false;
    }

    if (right_image.empty()) {
        std::cerr << "Failed to load right image: " << right_image_path << std::endl;
        return false;
    }

    cv::Mat disparity_map;
    if (!Inference(left_image, right_image, disparity_map)) {
        return false;
    }

    // 保存结果
    cv::Mat colored_disparity;
    VisualizeDisparity(disparity_map, colored_disparity);
    
    return cv::imwrite(output_path, colored_disparity);
}

void VisualizeDisparity(const cv::Mat& disparity_map, 
                       cv::Mat& colored_disparity, 
                       float max_disparity) {
    if (disparity_map.empty()) {
        std::cerr << "Input disparity map is empty!" << std::endl;
        return;
    }

    cv::Mat normalized_disparity;
    cv::Mat valid_mask = disparity_map > 0;
    
    if (cv::countNonZero(valid_mask) == 0) {
        std::cerr << "No valid disparity values found!" << std::endl;
        colored_disparity = cv::Mat::zeros(disparity_map.size(), CV_8UC3);
        return;
    }

    double min_val, max_val;
    cv::minMaxLoc(disparity_map, &min_val, &max_val, nullptr, nullptr, valid_mask);
    
    float actual_max = std::min(static_cast<float>(max_val), max_disparity);
    disparity_map.convertTo(normalized_disparity, CV_8U, 255.0 / actual_max, 0);
    
    cv::applyColorMap(normalized_disparity, colored_disparity, cv::COLORMAP_JET);
    colored_disparity.setTo(cv::Scalar(0, 0, 0), ~valid_mask);
}

void DisparityToDepth(const cv::Mat& disparity_map,
                     cv::Mat& depth_map,
                     const DepthParams& depth_params,
                     const ScaleInfo& scale_info) {
    if (disparity_map.empty()) {
        std::cerr << "Input disparity map is empty!" << std::endl;
        return;
    }
    
    if (depth_params.focal_length <= 0 || depth_params.baseline <= 0) {
        std::cerr << "Invalid camera parameters!" << std::endl;
        return;
    }
    
    // 校正视差值
    cv::Mat corrected_disparity;
    disparity_map.convertTo(corrected_disparity, CV_32F);
    corrected_disparity *= scale_info.scale_factor;
    
    // 计算深度：depth = (focal_length * baseline) / disparity
    depth_map = cv::Mat::zeros(disparity_map.size(), CV_32F);
    cv::Mat valid_mask = corrected_disparity > 0;
    
    float depth_factor = depth_params.focal_length * depth_params.baseline;
    cv::divide(depth_factor, corrected_disparity, depth_map, 1.0, CV_32F);
    depth_map.setTo(0, ~valid_mask);
}

void CorrectDisparityScale(const cv::Mat& disparity_map,
                          cv::Mat& corrected_disparity,
                          const ScaleInfo& scale_info) {
    if (disparity_map.empty()) {
        std::cerr << "Input disparity map is empty!" << std::endl;
        return;
    }
    
    disparity_map.convertTo(corrected_disparity, CV_32F);
    corrected_disparity *= scale_info.scale_factor;
}

} // namespace lightstereo_inference
