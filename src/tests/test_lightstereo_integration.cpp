#include <iostream>
#include <chrono>
#include <vector>
#include <string>
#include <opencv2/opencv.hpp>
#include "inference/stereo_inference.hpp"
#include "inference/lightstereo_inference.hpp"

/**
 * @brief 测试 LightStereo 集成到 StereoInference 的功能
 */
int main() {
    std::cout << "=== LightStereo Integration Test ===" << std::endl;
    
    try {
        // 1. 创建 StereoInference 实例
        std::cout << "1. 创建 StereoInference 实例..." << std::endl;
        StereoInference stereo_inference;
        
        // 2. 测试性能模式设置
        std::cout << "2. 测试性能模式设置..." << std::endl;
        stereo_inference.setPerformanceMode(StereoInference::ULTRA_FAST);
        std::cout << "   当前性能模式: " << stereo_inference.getPerformanceMode() << std::endl;
        
        // 3. 创建测试图像
        std::cout << "3. 创建测试图像..." << std::endl;
        cv::Size test_size(640, 480);
        cv::Mat left_image = cv::Mat::zeros(test_size, CV_8UC3);
        cv::Mat right_image = cv::Mat::zeros(test_size, CV_8UC3);
        
        // 添加一些简单的图案
        cv::rectangle(left_image, cv::Rect(100, 100, 200, 200), cv::Scalar(255, 255, 255), -1);
        cv::rectangle(right_image, cv::Rect(110, 100, 200, 200), cv::Scalar(255, 255, 255), -1);
        
        std::cout << "   测试图像尺寸: " << test_size << std::endl;
        
        // 4. 执行推理
        std::cout << "4. 执行立体视觉推理..." << std::endl;
        auto start_time = std::chrono::high_resolution_clock::now();
        
        cv::Mat disparity_map = stereo_inference.inference(left_image, right_image);
        
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
            end_time - start_time).count();
        
        std::cout << "   推理完成，耗时: " << duration << " ms" << std::endl;
        std::cout << "   输出视差图尺寸: " << disparity_map.size() << std::endl;
        
        // 5. 验证输出
        if (disparity_map.empty()) {
            std::cerr << "错误: 视差图为空!" << std::endl;
            return -1;
        }
        
        // 计算视差图统计信息
        double min_val, max_val;
        cv::minMaxLoc(disparity_map, &min_val, &max_val);
        std::cout << "   视差范围: [" << min_val << ", " << max_val << "]" << std::endl;
        
        // 6. 保存结果
        std::cout << "5. 保存测试结果..." << std::endl;
        cv::imwrite("test_left.png", left_image);
        cv::imwrite("test_right.png", right_image);
        
        // 保存视差图
        stereo_inference.saveDisparity(disparity_map, "test_disparity.png");
        
        std::cout << "   结果已保存到:" << std::endl;
        std::cout << "   - test_left.png" << std::endl;
        std::cout << "   - test_right.png" << std::endl;
        std::cout << "   - test_disparity.png" << std::endl;
        
        // 7. 测试不同性能模式
        std::cout << "6. 测试不同性能模式..." << std::endl;
        
        std::vector<StereoInference::PerformanceMode> modes = {
            StereoInference::ULTRA_FAST,
            StereoInference::FAST,
            StereoInference::BALANCED,
            StereoInference::HIGH_QUALITY
        };
        
        std::vector<std::string> mode_names = {
            "ULTRA_FAST", "FAST", "BALANCED", "HIGH_QUALITY"
        };
        
        for (size_t i = 0; i < modes.size(); i++) {
            std::cout << "   测试模式: " << mode_names[i] << std::endl;
            
            stereo_inference.setPerformanceMode(modes[i]);
            
            auto mode_start = std::chrono::high_resolution_clock::now();
            cv::Mat mode_disparity = stereo_inference.inference(left_image, right_image);
            auto mode_end = std::chrono::high_resolution_clock::now();
            
            auto mode_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                mode_end - mode_start).count();
            
            std::cout << "     耗时: " << mode_duration << " ms" << std::endl;
            std::cout << "     输出尺寸: " << mode_disparity.size() << std::endl;
            
            // 保存不同模式的结果
            std::string filename = "test_disparity_" + mode_names[i] + ".png";
            stereo_inference.saveDisparity(mode_disparity, filename);
            std::cout << "     结果保存到: " << filename << std::endl;
        }
        
        std::cout << "\n=== 测试完成 ===" << std::endl;
        std::cout << "✓ LightStereo 集成测试成功!" << std::endl;
        std::cout << "✓ 所有性能模式工作正常" << std::endl;
        std::cout << "✓ 接口兼容性良好" << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "测试失败: " << e.what() << std::endl;
        return -1;
    }
}
