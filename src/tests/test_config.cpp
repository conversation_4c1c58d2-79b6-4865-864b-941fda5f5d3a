#include "infrastructure/config/config_manager.h"
#include "infrastructure/config/config_validator.h"
#include <QCoreApplication>
#include <QDebug>
#include <QCommandLineParser>
#include <QDir>

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    app.setApplicationName("Smart Scope Qt Config Test");
    app.setApplicationVersion("0.1.0");
    
    qDebug() << "配置管理器测试程序启动";
    
    // 命令行参数解析
    QCommandLineParser parser;
    parser.setApplicationDescription("Smart Scope Qt 配置管理器测试程序");
    parser.addHelpOption();
    parser.addVersionOption();
    
    QCommandLineOption configOption(QStringList() << "c" << "config", 
                                   "指定配置文件路径", "config_file", "config.toml");
    parser.addOption(configOption);
    parser.process(app);
    
    // 加载配置
    QString configPath = parser.value(configOption);
    if (configPath.isEmpty()) {
        configPath = QDir::currentPath() + "/config.toml";
    }
    
    qDebug() << "尝试加载配置文件:" << configPath;
    
    if (!ConfigManager::instance().loadConfig(configPath)) {
        qWarning() << "无法加载配置文件:" << configPath;
        qWarning() << "将使用默认配置";
        ConfigManager::instance().setDefaultConfig();
    } else {
        qDebug() << "成功加载配置文件:" << ConfigManager::instance().getConfigPath();
    }
    
    // 测试获取配置值
    qDebug() << "\n===== 基本配置信息 =====";
    qDebug() << "应用程序名称:" << ConfigManager::instance().getValue("app.name").toString();
    qDebug() << "应用程序版本:" << ConfigManager::instance().getValue("app.version").toString();
    qDebug() << "日志级别:" << ConfigManager::instance().getValue("app.log_level").toString();
    qDebug() << "日志文件:" << ConfigManager::instance().getValue("app.log_file").toString();
    
    qDebug() << "\n===== 相机配置 =====";
    qDebug() << "相机宽度:" << ConfigManager::instance().getValue("camera.width").toInt();
    qDebug() << "相机高度:" << ConfigManager::instance().getValue("camera.height").toInt();
    qDebug() << "相机帧率:" << ConfigManager::instance().getValue("camera.fps").toInt();
    
    qDebug() << "\n===== 左相机配置 =====";
    QVariantList leftCameraNames = ConfigManager::instance().getValue("camera.left.name").toList();
    qDebug() << "左相机名称:";
    for (const QVariant& name : leftCameraNames) {
        qDebug() << "  -" << name.toString();
    }
    qDebug() << "左相机参数路径:" << ConfigManager::instance().getValue("camera.left.parameters_path").toString();
    
    qDebug() << "\n===== 右相机配置 =====";
    QVariantList rightCameraNames = ConfigManager::instance().getValue("camera.right.name").toList();
    qDebug() << "右相机名称:";
    for (const QVariant& name : rightCameraNames) {
        qDebug() << "  -" << name.toString();
    }
    qDebug() << "右相机参数路径:" << ConfigManager::instance().getValue("camera.right.parameters_path").toString();
    
    // 测试配置验证
    qDebug() << "\n===== 配置验证测试 =====";
    ConfigValidator validator;
    
    // 添加必需的键
    validator.addRequiredKey("app.name");
    validator.addRequiredKey("app.version");
    validator.addRequiredKey("camera.width");
    validator.addRequiredKey("camera.height");
    
    // 添加验证规则
    validator.addRule("camera.width", ConfigValidator::rangeRule(640, 4096));
    validator.addRule("camera.height", ConfigValidator::rangeRule(480, 2160));
    validator.addRule("camera.fps", ConfigValidator::rangeRule(1, 120));
    validator.addRule("stereo.matcher", ConfigValidator::enumRule(QStringList({"bm", "sgbm", "neural"})));
    
    // 验证配置
    bool isValid = validator.validate(ConfigManager::instance().getAllConfig());
    
    if (isValid) {
        qDebug() << "配置验证通过";
    } else {
        qDebug() << "配置验证失败:";
        for (const QString& error : validator.getErrors()) {
            qDebug() << "  -" << error;
        }
    }
    
    // 测试修改配置
    qDebug() << "\n===== 配置修改测试 =====";
    qDebug() << "修改前的相机宽度:" << ConfigManager::instance().getValue("camera.width").toInt();
    
    // 修改配置
    ConfigManager::instance().setValue("camera.width", 1920);
    qDebug() << "修改后的相机宽度:" << ConfigManager::instance().getValue("camera.width").toInt();
    
    // 添加新的配置项
    ConfigManager::instance().setValue("test.new_item", "这是一个新的配置项");
    qDebug() << "新配置项的值:" << ConfigManager::instance().getValue("test.new_item").toString();
    
    // 保存修改后的配置
    QString newConfigPath = QDir::currentPath() + "/config_modified.toml";
    if (ConfigManager::instance().saveConfig(newConfigPath)) {
        qDebug() << "修改后的配置已保存到:" << newConfigPath;
    } else {
        qDebug() << "保存修改后的配置失败";
    }
    
    qDebug() << "\n配置管理器测试程序结束";
    
    return 0;
} 