#include <QCoreApplication>
#include <QCommandLineParser>
#include <QDebug>
#include <QThread>
#include <QElapsedTimer>
#include <QMutex>
#include <QDir>
#include <QFile>
#include <QProcess>
#include <QRegularExpression>
#include <QTimer>
#include <QScopedPointer>
#include <fcntl.h>
#include <unistd.h>
#include <QDateTime>
#include <QTextStream>
#include <functional>
#include <future>
#include <chrono>
#include <iostream>
#include <signal.h>

#include "core/camera/camera_manager.h"
#include "core/camera/camera.h"
#include "core/camera/usb_camera.h"
#include "infrastructure/config/config_manager.h"
#include "infrastructure/logging/logger.h"
#include "infrastructure/exception/app_exception.h"
#include "infrastructure/exception/camera_exception.h"

using namespace SmartScope;
using namespace Core;
using namespace SmartScope::Infrastructure;

volatile sig_atomic_t g_running = 1;

void signalHandler(int sig) {
    LOG_INFO(QString("接收到信号 %1，准备退出").arg(sig));
    g_running = 0;
}

// 帧处理类
class FrameProcessor : public QObject {
    Q_OBJECT
public:
    FrameProcessor(QObject* parent = nullptr) : QObject(parent), frameCount(0) {
        timer.start();
    }

public slots:
    void processFrame(const QImage& frame) {
        QMutexLocker locker(&mutex);
        frameCount++;
        
        // 每秒计算一次帧率
        if (timer.elapsed() >= 1000) {
            double fps = frameCount / (timer.elapsed() / 1000.0);
            qDebug() << "处理帧率:" << fps << "FPS";
            frameCount = 0;
            timer.restart();
        }
        
        // 处理帧（这里只是简单地记录帧的大小）
        qDebug() << "处理帧:" << frame.width() << "x" << frame.height();
        
        // 保存第一帧
        if (savedFrames < 1) {
            QString filename = QString("frame_%1.jpg").arg(savedFrames);
            frame.save(filename);
            qDebug() << "帧已保存为" << filename;
            savedFrames++;
        }
    }

private:
    QMutex mutex;
    QElapsedTimer timer;
    int frameCount;
    int savedFrames = 0;
};

// 查找相机设备
QStringList findCameraDevices() {
    QStringList result;
    QDir dir("/dev");
    QStringList filters;
    filters << "video*";
    QStringList devices = dir.entryList(filters, QDir::System);
    
    for (const QString& device : devices) {
        QString devicePath = "/dev/" + device;
        QFile deviceFile(devicePath);
        if (deviceFile.exists() && deviceFile.open(QIODevice::ReadOnly)) {
            deviceFile.close();
            result << devicePath;
        }
    }
    
    return result;
}

// 获取设备总线信息
QString getBusInfo(const QString& devicePath) {
    QProcess process;
    process.start("v4l2-ctl", QStringList() << "--device=" + devicePath << "--info");
    
    if (!process.waitForFinished(1000)) {
        return QString();
    }
    
    QString output = QString::fromUtf8(process.readAllStandardOutput());
    QRegularExpression busRegex("Bus info\\s+:\\s+(.+)$", QRegularExpression::MultilineOption);
    QRegularExpressionMatch match = busRegex.match(output);
    
    if (match.hasMatch()) {
        return match.captured(1).trimmed();
    }
    
    return QString();
}

// 带超时的函数执行封装
template<typename Func, typename... Args>
bool runWithTimeout(const QString& operationName, int timeoutMs, Func&& func, Args&&... args) {
    try {
        auto future = std::async(std::launch::async, std::forward<Func>(func), std::forward<Args>(args)...);
        
        if (future.wait_for(std::chrono::milliseconds(timeoutMs)) == std::future_status::timeout) {
            LOG_WARNING(QString("%1 操作超时").arg(operationName));
            return false;
        }
        
        return future.get();
    } catch (const CameraException& e) {
        LOG_ERROR(QString("%1 操作失败: %2")
                 .arg(operationName)
                 .arg(e.getMessage()));
        return false;
    } catch (const std::exception& e) {
        LOG_ERROR(QString("%1 操作失败: %2").arg(operationName).arg(e.what()));
        return false;
    }
}

// 安全地检查相机设备是否可访问（不会阻塞）
bool isCameraAccessible(const QString& devicePath) {
    // 使用非阻塞模式打开设备
    int fd = ::open(devicePath.toStdString().c_str(), O_RDWR | O_NONBLOCK);
    if (fd < 0) {
        qWarning() << "无法打开相机设备:" << devicePath << "错误:" << errno;
        return false;
    }
    
    // 关闭设备
    ::close(fd);
    return true;
}

// 初始化日志系统
void initLogger() {
    LOG_INFO("开始初始化日志系统");
    
    // 配置日志系统
    Logger::instance().setLogLevel(LogLevel::DEBUG);
    Logger::instance().setConsoleEnabled(true);
    
    // 设置文件输出
    QString logFileName = QString("camera_test_%1.log").arg(
        QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss"));
    Logger::instance().setFileEnabled(true);
    Logger::instance().setLogFilePath(logFileName);
    
    LOG_INFO("日志系统初始化完成");
}

// 简单测试：初始化相机管理器，检查设备可访问性
bool simpleTest() {
    LOG_INFO("======= 开始简单测试 =======");
    
    try {
        // 初始化相机管理器
        LOG_INFO("初始化相机管理器");
        CameraManager& manager = CameraManager::instance();
        
        if (!manager.initialize()) {
            LOG_ERROR("初始化相机管理器失败");
            return false;
        }
        
        // 刷新相机列表
        LOG_INFO("刷新相机列表");
        if (!manager.refreshCameraList()) {
            LOG_ERROR("刷新相机列表失败");
            return false;
        }
        
        // 获取可用相机列表
        QStringList cameras = manager.getAvailableCameras();
        LOG_INFO(QString("找到 %1 个相机设备").arg(cameras.size()));
        
        for (const QString& camera : cameras) {
            LOG_INFO(QString("相机设备: %1").arg(camera));
        }
        
        // 关闭相机管理器
        LOG_INFO("关闭相机管理器");
        manager.shutdown();
        
        LOG_INFO("======= 简单测试完成 =======");
        return true;
    }
    catch (const AppException& e) {
        LOG_ERROR(QString("测试异常: %1").arg(e.getFormattedMessage()));
        return false;
    }
    catch (const std::exception& e) {
        LOG_ERROR(QString("测试标准异常: %1").arg(e.what()));
        return false;
    }
    catch (...) {
        LOG_ERROR("测试未知异常");
        return false;
    }
}

// 相机检查测试：检查左右相机匹配和打开
bool cameraCheckTest() {
    try {
        LOG_INFO("开始相机检查测试");
        
        // 创建相机管理器
        CameraManager& manager = CameraManager::instance();
        
        // 初始化相机管理器
        if (!runWithTimeout("初始化相机管理器", 5000, [&manager]() {
            return manager.initialize();
        })) {
            LOG_ERROR("初始化相机管理器失败");
            return false;
        }
        
        // 获取可用相机
        QStringList availableCameras = manager.getAvailableCameras();
        LOG_INFO(QString("可用相机: %1").arg(availableCameras.join(", ")));
        
        if (availableCameras.isEmpty()) {
            LOG_WARNING("未检测到可用相机");
            return false;
        }
        
        // 选择左右相机
        QString leftCameraId;
        QString rightCameraId;
        
        if (availableCameras.size() >= 2) {
            leftCameraId = availableCameras[0];
            rightCameraId = availableCameras[1];
        } else {
            leftCameraId = availableCameras[0];
            rightCameraId = availableCameras[0];
        }
        
        LOG_INFO(QString("左相机: %1").arg(leftCameraId));
        LOG_INFO(QString("右相机: %1").arg(rightCameraId));
        
        // 设置左右相机
        if (!leftCameraId.isEmpty()) {
            LOG_INFO(QString("设置左相机: %1").arg(leftCameraId));
            if (!runWithTimeout("设置左相机", 5000, [&manager, &leftCameraId]() {
                return manager.setLeftCamera(leftCameraId);
            })) {
                LOG_ERROR("设置左相机超时或失败");
            }
        }
        
        if (!rightCameraId.isEmpty()) {
            LOG_INFO(QString("设置右相机: %1").arg(rightCameraId));
            if (!runWithTimeout("设置右相机", 5000, [&manager, &rightCameraId]() {
                return manager.setRightCamera(rightCameraId);
            })) {
                LOG_ERROR("设置右相机超时或失败");
            }
        }
        
        // 检查左右相机对象
        Camera* leftCamera = manager.getLeftCamera();
        Camera* rightCamera = manager.getRightCamera();
        
        if (leftCamera) {
            LOG_INFO(QString("左相机ID: %1").arg(leftCamera->getDeviceId()));
            LOG_INFO(QString("左相机初始化成功"));
        } else {
            LOG_WARNING("左相机对象为空");
        }
        
        if (rightCamera) {
            LOG_INFO(QString("右相机ID: %1").arg(rightCamera->getDeviceId()));
            LOG_INFO(QString("右相机初始化成功"));
        } else {
            LOG_WARNING("右相机对象为空");
        }
        
        // 关闭相机管理器
        LOG_INFO("关闭相机管理器");
        if (!runWithTimeout("关闭相机管理器", 5000, [&manager]() {
            manager.shutdown();
            return true;
        })) {
            LOG_ERROR("关闭相机管理超时");
        }
        
        return (leftCamera != nullptr && rightCamera != nullptr);
    } catch (const std::exception& e) {
        LOG_ERROR(QString("相机检查测试异常: %1").arg(e.what()));
        return false;
    }
}

// 相机捕获测试：启动相机捕获并获取帧
bool cameraCaptureTest() {
    LOG_INFO("======= 开始相机捕获测试 =======");
    
    try {
        // 初始化相机管理器
        LOG_INFO("初始化相机管理器");
        CameraManager& manager = CameraManager::instance();
        
        if (!manager.initialize()) {
            LOG_ERROR("初始化相机管理器失败");
            return false;
        }
        
        // 刷新相机列表
        LOG_INFO("刷新相机列表");
        if (!manager.refreshCameraList()) {
            LOG_ERROR("刷新相机列表失败");
            return false;
        }
        
        // 获取可用相机列表
        QStringList cameras = manager.getAvailableCameras();
        LOG_INFO(QString("找到 %1 个相机设备").arg(cameras.size()));
        
        if (cameras.isEmpty()) {
            LOG_WARNING("没有找到相机设备");
            return false;
        }
        
        // 设置左右相机（简单测试，使用第一和第二个相机）
        QString leftCameraId = cameras.size() > 0 ? cameras[0] : "";
        QString rightCameraId = cameras.size() > 1 ? cameras[1] : "";
        
        if (!leftCameraId.isEmpty()) {
            LOG_INFO(QString("设置左相机: %1").arg(leftCameraId));
            if (!runWithTimeout("设置左相机", 5000, [&manager, &leftCameraId]() {
                return manager.setLeftCamera(leftCameraId);
            })) {
                LOG_ERROR("设置左相机超时或失败");
            }
        }
        
        if (!rightCameraId.isEmpty()) {
            LOG_INFO(QString("设置右相机: %1").arg(rightCameraId));
            if (!runWithTimeout("设置右相机", 5000, [&manager, &rightCameraId]() {
                return manager.setRightCamera(rightCameraId);
            })) {
                LOG_ERROR("设置右相机超时或失败");
            }
        }
        
        // 开始捕获
        LOG_INFO("开始捕获");
        if (!runWithTimeout("开始捕获", 5000, [&manager]() {
            return manager.startCapture();
        })) {
            LOG_ERROR("开始捕获超时或失败");
            return false;
        }
        
        // 捕获10秒
        LOG_INFO("捕获10秒...");
        int frameCount = 0;
        auto startTime = std::chrono::steady_clock::now();
        
        while (g_running) {
            auto now = std::chrono::steady_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - startTime).count();
            
            if (elapsed >= 10) {
                break;
            }
            
            // 处理事件
            QCoreApplication::processEvents();
            
            // 短暂睡眠，减少CPU使用
            QThread::msleep(10);
        }
        
        // 计算帧率
        auto endTime = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count();
        double fps = frameCount / (duration / 1000.0);
        
        LOG_INFO(QString("捕获结束，共获取 %1 帧，平均帧率: %2 fps").arg(frameCount).arg(fps, 0, 'f', 2));
        
        // 停止捕获
        LOG_INFO("停止捕获");
        if (!runWithTimeout("停止捕获", 5000, [&manager]() {
            manager.stopCapture();
            return true;
        })) {
            LOG_ERROR("停止捕获超时");
        }
        
        // 关闭相机管理器
        LOG_INFO("关闭相机管理器");
        if (!runWithTimeout("关闭相机管理器", 5000, [&manager]() {
            manager.shutdown();
            return true;
        })) {
            LOG_ERROR("关闭相机管理器超时");
        }
        
        LOG_INFO("======= 相机捕获测试完成 =======");
        return true;
    }
    catch (const AppException& e) {
        LOG_ERROR(QString("测试异常: %1").arg(e.getFormattedMessage()));
        return false;
    }
    catch (const std::exception& e) {
        LOG_ERROR(QString("测试标准异常: %1").arg(e.what()));
        return false;
    }
    catch (...) {
        LOG_ERROR("测试未知异常");
        return false;
    }
}

// 测试相机状态检查
bool testCameraStatus(CameraManager& cameraManager) {
    LOG_INFO("开始测试相机状态检查功能");
    
    try {
        // 获取所有可用相机
        bool refreshed = runWithTimeout("刷新相机列表", 5000, 
            [&cameraManager]() { return cameraManager.refreshCameraList(); });
        
        if (!refreshed) {
            LOG_ERROR("刷新相机列表失败，无法继续测试");
            return false;
        }
        
        QStringList cameras = cameraManager.getAvailableCameras();
        if (cameras.isEmpty()) {
            LOG_WARNING("未检测到可用相机，无法测试状态功能");
            return false;
        }
        
        // 对每个相机测试状态检查
        for (const QString& cameraId : cameras) {
            LOG_INFO(QString("测试相机 %1 的状态功能").arg(cameraId));
            
            // 检查访问性
            bool accessible = cameraManager.isCameraAccessible(cameraId);
            LOG_INFO(QString("相机 %1 可访问性: %2").arg(cameraId).arg(accessible ? "是" : "否"));
            
            // 获取总线信息
            QString busInfo = cameraManager.getCameraBusInfo(cameraId);
            LOG_INFO(QString("相机 %1 总线信息: %2").arg(cameraId).arg(busInfo.isEmpty() ? "未获取到" : busInfo));
            
            // 检查使用状态
            bool inUse = cameraManager.isCameraInUse(cameraId);
            LOG_INFO(QString("相机 %1 使用状态: %2").arg(cameraId).arg(inUse ? "使用中" : "未使用"));
            
            // 获取综合状态
            QString status = cameraManager.getCameraStatus(cameraId);
            LOG_INFO(QString("相机 %1 综合状态: %2").arg(cameraId).arg(status));
            
            // 尝试打开相机，再次检查状态
            if (!inUse && accessible) {
                LOG_INFO(QString("尝试打开相机 %1 以测试状态变化").arg(cameraId));
                bool opened = cameraManager.openCamera(cameraId);
                
                if (opened) {
                    LOG_INFO(QString("相机 %1 成功打开").arg(cameraId));
                    
                    // 再次检查状态
                    inUse = cameraManager.isCameraInUse(cameraId);
                    LOG_INFO(QString("相机 %1 打开后使用状态: %2").arg(cameraId).arg(inUse ? "使用中" : "未使用"));
                    
                    status = cameraManager.getCameraStatus(cameraId);
                    LOG_INFO(QString("相机 %1 打开后综合状态: %2").arg(cameraId).arg(status));
                    
                    // 关闭相机
                    cameraManager.closeCamera(cameraId);
                    LOG_INFO(QString("相机 %1 已关闭").arg(cameraId));
                } else {
                    LOG_WARNING(QString("无法打开相机 %1 进行测试").arg(cameraId));
                }
            }
        }
        
        LOG_INFO("相机状态检查功能测试完成");
        return true;
    } catch (const std::exception& e) {
        LOG_ERROR(QString("相机状态测试过程发生异常: %1").arg(e.what()));
        return false;
    }
}

// 测试相机恢复功能
bool testCameraRecovery(CameraManager& cameraManager) {
    LOG_INFO("开始测试相机恢复功能");
    
    try {
        QStringList cameras = cameraManager.getAvailableCameras();
        if (cameras.isEmpty()) {
            LOG_WARNING("未检测到可用相机，无法测试恢复功能");
            return false;
        }
        
        // 选择第一个相机测试恢复功能
        QString testCamera = cameras.first();
        LOG_INFO(QString("使用相机 %1 测试恢复功能").arg(testCamera));
        
        // 先尝试打开相机
        if (cameraManager.openCamera(testCamera)) {
            LOG_INFO(QString("相机 %1 打开成功，模拟异常情况").arg(testCamera));
            
            // 强制关闭相机（模拟异常关闭）
            cameraManager.closeCamera(testCamera);
            LOG_INFO("相机已强制关闭，尝试恢复");
            
            // 尝试恢复相机
            bool recovered = cameraManager.recoverCamera(testCamera);
            LOG_INFO(QString("相机恢复%1").arg(recovered ? "成功" : "失败"));
            
            // 如果恢复成功，再次关闭
            if (recovered) {
                cameraManager.closeCamera(testCamera);
                LOG_INFO("已关闭恢复后的相机");
            }
            
            return recovered;
        } else {
            LOG_WARNING(QString("无法打开相机 %1 进行恢复测试").arg(testCamera));
            return false;
        }
    } catch (const std::exception& e) {
        LOG_ERROR(QString("相机恢复测试过程发生异常: %1").arg(e.what()));
        return false;
    }
}

// 测试异常处理
bool testExceptionHandling(CameraManager& cameraManager) {
    LOG_INFO("开始测试相机异常处理");
    
    try {
        // 测试无效相机ID
        LOG_INFO("测试打开无效相机ID");
        bool success = false;
        
        try {
            // 故意使用无效ID
            bool opened = cameraManager.openCamera("invalid_camera_id");
            LOG_WARNING("打开无效相机ID未抛出异常");
            
            // 如果意外成功，关闭相机
            if (opened) {
                LOG_ERROR("打开无效相机ID竟然成功了，这不符合预期");
                cameraManager.closeCamera("invalid_camera_id");
                return false;
            }
            
            // 虽然没抛出异常，但函数返回false也是可接受的行为
            LOG_INFO("打开无效相机ID返回false，符合预期");
            success = true;
        } catch (const CameraException& e) {
            // 捕获到CameraException异常，正是我们期望的结果
            LOG_INFO(QString("成功捕获相机异常: %1").arg(e.getMessage()));
            success = true;
        } catch (const std::exception& e) {
            // 捕获到其他标准异常，也可以接受
            LOG_INFO(QString("捕获标准异常: %1").arg(e.what()));
            success = true;
        }
        
        if (success) {
            LOG_INFO("相机异常处理测试通过");
        } else {
            LOG_ERROR("相机异常处理测试失败");
        }
        
        return success;
    } catch (const std::exception& e) {
        LOG_ERROR(QString("异常处理测试过程发生意外异常: %1").arg(e.what()));
        return false;
    }
}

// 主函数
int main(int argc, char *argv[])
{
    // 设置信号处理
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
    
    // 创建Qt应用程序
    QCoreApplication app(argc, argv);
    
    // 初始化日志系统
    initLogger();
    
    LOG_INFO("======= 相机测试程序开始 =======");
    
    // 获取相机管理器单例
    CameraManager& cameraManager = CameraManager::instance();
    
    // 初始化相机管理器
    bool initialized = runWithTimeout("初始化相机管理器", 5000, 
        [&cameraManager]() { return cameraManager.initialize(); });
    
    if (!initialized) {
        LOG_ERROR("相机管理器初始化失败，测试终止");
        return 1;
    }
    
    LOG_INFO("相机管理器初始化成功，开始测试");
    
    // 测试相机状态功能
    bool statusTestResult = testCameraStatus(cameraManager);
    LOG_INFO(QString("相机状态测试%1").arg(statusTestResult ? "通过" : "失败"));
    
    // 测试相机恢复功能
    bool recoveryTestResult = testCameraRecovery(cameraManager);
    LOG_INFO(QString("相机恢复测试%1").arg(recoveryTestResult ? "通过" : "失败"));
    
    // 测试异常处理
    bool exceptionTestResult = testExceptionHandling(cameraManager);
    LOG_INFO(QString("异常处理测试%1").arg(exceptionTestResult ? "通过" : "失败"));
    
    // 关闭相机管理器
    cameraManager.shutdown();
    LOG_INFO("相机管理器已关闭");
    
    LOG_INFO("======= 相机测试程序结束 =======");
    
    return (statusTestResult && recoveryTestResult && exceptionTestResult) ? 0 : 1;
}

#include "test_camera.moc" 