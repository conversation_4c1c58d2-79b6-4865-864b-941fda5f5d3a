#include "infrastructure/config/config_loader.h"
#include <QCoreApplication>
#include <QDebug>
#include <QCommandLineParser>
#include <QDir>
#include <QFile>

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    app.setApplicationName("Smart Scope Qt Config Test Simple");
    app.setApplicationVersion("0.1.0");
    
    qDebug() << "配置加载器测试程序启动";
    
    // 命令行参数解析
    QCommandLineParser parser;
    parser.setApplicationDescription("Smart Scope Qt 配置加载器测试程序");
    parser.addHelpOption();
    parser.addVersionOption();
    
    QCommandLineOption configOption(QStringList() << "c" << "config", 
                                   "指定配置文件路径", "config_file", "config.toml");
    parser.addOption(configOption);
    parser.process(app);
    
    // 加载配置
    QString configPath = parser.value(configOption);
    if (configPath.isEmpty()) {
        configPath = QDir::currentPath() + "/config.toml";
    }
    
    qDebug() << "尝试加载配置文件:" << configPath;
    
    // 检查文件是否存在
    if (!QFile::exists(configPath)) {
        qWarning() << "配置文件不存在:" << configPath;
        return 1;
    }
    
    // 使用配置加载器加载文件
    ConfigLoader loader;
    QVariantMap config = loader.loadFromFile(configPath);
    
    if (config.isEmpty()) {
        qWarning() << "配置文件加载失败:" << loader.getLastError();
        return 1;
    }
    
    qDebug() << "成功加载配置文件:" << configPath;
    
    // 测试获取配置值
    qDebug() << "\n===== 基本配置信息 =====";
    QVariantMap appConfig = config["app"].toMap();
    qDebug() << "应用程序名称:" << appConfig["name"].toString();
    qDebug() << "应用程序版本:" << appConfig["version"].toString();
    qDebug() << "日志级别:" << appConfig["log_level"].toString();
    qDebug() << "日志文件:" << appConfig["log_file"].toString();
    
    qDebug() << "\n===== 相机配置 =====";
    QVariantMap cameraConfig = config["camera"].toMap();
    qDebug() << "相机宽度:" << cameraConfig["width"].toInt();
    qDebug() << "相机高度:" << cameraConfig["height"].toInt();
    qDebug() << "相机帧率:" << cameraConfig["fps"].toInt();
    
    qDebug() << "\n===== 左相机配置 =====";
    QVariantMap leftCameraConfig = cameraConfig["left"].toMap();
    QVariantList leftCameraNames = leftCameraConfig["name"].toList();
    qDebug() << "左相机名称:";
    for (const QVariant& name : leftCameraNames) {
        qDebug() << "  -" << name.toString();
    }
    qDebug() << "左相机参数路径:" << leftCameraConfig["parameters_path"].toString();
    
    qDebug() << "\n===== 右相机配置 =====";
    QVariantMap rightCameraConfig = cameraConfig["right"].toMap();
    QVariantList rightCameraNames = rightCameraConfig["name"].toList();
    qDebug() << "右相机名称:";
    for (const QVariant& name : rightCameraNames) {
        qDebug() << "  -" << name.toString();
    }
    qDebug() << "右相机参数路径:" << rightCameraConfig["parameters_path"].toString();
    
    // 测试修改配置
    qDebug() << "\n===== 配置修改测试 =====";
    qDebug() << "修改前的相机宽度:" << cameraConfig["width"].toInt();
    
    // 修改配置
    cameraConfig["width"] = 1920;
    config["camera"] = cameraConfig;
    qDebug() << "修改后的相机宽度:" << config["camera"].toMap()["width"].toInt();
    
    // 添加新的配置项
    QVariantMap testConfig;
    testConfig["new_item"] = "这是一个新的配置项";
    config["test"] = testConfig;
    qDebug() << "新配置项的值:" << config["test"].toMap()["new_item"].toString();
    
    // 保存修改后的配置
    QString newConfigPath = QDir::currentPath() + "/config_modified.toml";
    if (loader.saveToFile(config, newConfigPath)) {
        qDebug() << "修改后的配置已保存到:" << newConfigPath;
    } else {
        qDebug() << "保存修改后的配置失败:" << loader.getLastError();
    }
    
    qDebug() << "\n配置加载器测试程序结束";
    
    return 0;
} 