#include <QCoreApplication>
#include <QDebug>
#include <QThread>
#include <QMutex>
#include <QWaitCondition>
#include <QElapsedTimer>
#include <QTimer>
#include <QEventLoop>
#include <QDir>

#include "infrastructure/logging/logger.h"
#include "infrastructure/config/config_manager.h"
#include "infrastructure/exception/app_exception.h"

// 测试场景枚举
enum TestScenario {
    CONFIG_FIRST_LOG_SECOND,    // 先初始化配置，后初始化日志
    LOG_FIRST_CONFIG_SECOND,    // 先初始化日志，后初始化配置
    CONFIG_ONLY,                // 只初始化配置
    LOG_ONLY,                   // 只初始化日志
    PARALLEL_INIT               // 并行初始化
};

// 测试结果结构
struct TestResult {
    bool success;
    QString errorMessage;
    qint64 elapsedMs;
};

// 初始化日志模块
TestResult initLogger() {
    TestResult result;
    QElapsedTimer timer;
    timer.start();
    
    try {
        qDebug() << "开始初始化日志模块...";
        
        // 检查日志目录
        QDir logDir("logs");
        if (!logDir.exists()) {
            qDebug() << "创建日志目录...";
            if (!logDir.mkpath(".")) {
                qDebug() << "创建日志目录失败!";
                result.success = false;
                result.errorMessage = "创建日志目录失败";
                return result;
            }
        }
        
        qDebug() << "调用Logger::instance()...";
        Logger& logger = Logger::instance();
        qDebug() << "Logger::instance()调用成功";
        
        qDebug() << "调用logger.init()，禁用文件输出...";
        // 禁用文件输出，只使用控制台输出
        bool success = logger.init("", LogLevel::DEBUG, true, false);
        qDebug() << "logger.init()调用完成，结果:" << success;
        
        if (success) {
            qDebug() << "日志模块初始化成功，尝试记录日志...";
            logger.info("日志模块初始化成功");
            qDebug() << "日志记录成功";
            result.success = true;
        } else {
            result.success = false;
            result.errorMessage = "日志模块初始化失败";
        }
    } catch (const AppException& e) {
        result.success = false;
        result.errorMessage = QString("日志模块初始化异常(AppException): %1").arg(e.what());
    } catch (const std::exception& e) {
        result.success = false;
        result.errorMessage = QString("日志模块初始化异常(std::exception): %1").arg(e.what());
    } catch (...) {
        result.success = false;
        result.errorMessage = "日志模块初始化异常(未知异常)";
    }
    
    result.elapsedMs = timer.elapsed();
    qDebug() << "日志模块初始化" << (result.success ? "成功" : "失败") 
             << "，耗时:" << result.elapsedMs << "ms";
    if (!result.success) {
        qDebug() << "错误信息:" << result.errorMessage;
    }
    
    return result;
}

// 初始化配置模块
TestResult initConfig(const QString& configPath = "config.toml") {
    TestResult result;
    QElapsedTimer timer;
    timer.start();
    
    try {
        qDebug() << "开始初始化配置模块...";
        bool success = ConfigManager::instance().loadConfig(configPath);
        if (success) {
            qDebug() << "配置模块初始化成功，尝试读取配置...";
            QString appName = ConfigManager::instance().getValue("app.name").toString();
            qDebug() << "读取配置成功: app.name =" << appName;
            result.success = true;
        } else {
            qDebug() << "配置文件加载失败，使用默认配置";
            ConfigManager::instance().setDefaultConfig();
            result.success = true;
        }
    } catch (const AppException& e) {
        result.success = false;
        result.errorMessage = QString("配置模块初始化异常(AppException): %1").arg(e.what());
    } catch (const std::exception& e) {
        result.success = false;
        result.errorMessage = QString("配置模块初始化异常(std::exception): %1").arg(e.what());
    } catch (...) {
        result.success = false;
        result.errorMessage = "配置模块初始化异常(未知异常)";
    }
    
    result.elapsedMs = timer.elapsed();
    qDebug() << "配置模块初始化" << (result.success ? "成功" : "失败") 
             << "，耗时:" << result.elapsedMs << "ms";
    if (!result.success) {
        qDebug() << "错误信息:" << result.errorMessage;
    }
    
    return result;
}

// 并行初始化线程函数
void parallelInitThread(TestScenario scenario, TestResult* logResult, TestResult* configResult, 
                       QMutex* mutex, QWaitCondition* condition) {
    if (scenario == CONFIG_FIRST_LOG_SECOND) {
        qDebug() << "执行顺序: 先配置后日志";
        *configResult = initConfig();
        
        // 处理事件循环，确保信号被处理
        QCoreApplication::processEvents();
        
        *logResult = initLogger();
    } else if (scenario == LOG_FIRST_CONFIG_SECOND) {
        qDebug() << "执行顺序: 先日志后配置";
        *logResult = initLogger();
        
        // 处理事件循环，确保信号被处理
        QCoreApplication::processEvents();
        
        *configResult = initConfig();
    } else if (scenario == CONFIG_ONLY) {
        qDebug() << "执行顺序: 仅配置";
        *configResult = initConfig();
        logResult->success = true;
        logResult->errorMessage = "未执行";
        logResult->elapsedMs = 0;
    } else if (scenario == LOG_ONLY) {
        qDebug() << "执行顺序: 仅日志";
        *logResult = initLogger();
        configResult->success = true;
        configResult->errorMessage = "未执行";
        configResult->elapsedMs = 0;
    } else if (scenario == PARALLEL_INIT) {
        qDebug() << "执行顺序: 并行初始化";
        
        // 使用事件循环来处理并行初始化
        QEventLoop loop;
        QTimer::singleShot(5000, &loop, &QEventLoop::quit); // 5秒超时
        
        QThread* logThread = QThread::create([logResult]() {
            *logResult = initLogger();
        });
        
        QThread* configThread = QThread::create([configResult]() {
            *configResult = initConfig();
        });
        
        // 连接线程完成信号
        QObject::connect(logThread, &QThread::finished, [&loop, logThread]() {
            qDebug() << "日志线程完成";
            logThread->deleteLater();
            
            // 如果配置线程也完成了，退出事件循环
            if (!loop.isRunning()) {
                loop.quit();
            }
        });
        
        QObject::connect(configThread, &QThread::finished, [&loop, configThread]() {
            qDebug() << "配置线程完成";
            configThread->deleteLater();
            
            // 如果日志线程也完成了，退出事件循环
            if (!loop.isRunning()) {
                loop.quit();
            }
        });
        
        // 启动线程
        logThread->start();
        configThread->start();
        
        // 等待线程完成或超时
        loop.exec();
        
        // 如果线程仍在运行，强制终止
        if (logThread->isRunning()) {
            qDebug() << "日志线程超时，强制终止";
            logThread->terminate();
            logResult->success = false;
            logResult->errorMessage = "线程超时";
        }
        
        if (configThread->isRunning()) {
            qDebug() << "配置线程超时，强制终止";
            configThread->terminate();
            configResult->success = false;
            configResult->errorMessage = "线程超时";
        }
    }
    
    // 处理事件循环，确保信号被处理
    QCoreApplication::processEvents();
    
    mutex->lock();
    condition->wakeAll();
    mutex->unlock();
}

// 运行测试场景
void runTestScenario(TestScenario scenario, const QString& scenarioName) {
    qDebug() << "\n========== 测试场景: " << scenarioName << " ==========";
    
    TestResult logResult;
    TestResult configResult;
    
    QMutex mutex;
    QWaitCondition condition;
    
    // 创建事件循环
    QEventLoop loop;
    
    // 创建线程运行测试
    QThread* thread = QThread::create([&]() {
        parallelInitThread(scenario, &logResult, &configResult, &mutex, &condition);
    });
    
    // 连接线程完成信号
    QObject::connect(thread, &QThread::finished, [&loop, thread]() {
        qDebug() << "测试线程完成";
        thread->deleteLater();
        loop.quit();
    });
    
    // 设置超时
    QTimer::singleShot(5000, &loop, [&loop, &thread]() {
        qDebug() << "测试场景超时!";
        if (thread->isRunning()) {
            thread->terminate();
        }
        loop.quit();
    });
    
    QElapsedTimer totalTimer;
    totalTimer.start();
    
    // 启动线程
    thread->start();
    
    // 等待测试完成或超时
    mutex.lock();
    bool finished = condition.wait(&mutex, 5000); // 5秒超时
    mutex.unlock();
    
    if (!finished) {
        qDebug() << "测试场景超时!";
        if (thread->isRunning()) {
            thread->terminate();
        }
    } else {
        qint64 totalElapsed = totalTimer.elapsed();
        
        qDebug() << "测试场景总耗时:" << totalElapsed << "ms";
        qDebug() << "日志模块:" << (logResult.success ? "成功" : "失败");
        qDebug() << "配置模块:" << (configResult.success ? "成功" : "失败");
        
        if (!logResult.success) {
            qDebug() << "日志错误:" << logResult.errorMessage;
        }
        
        if (!configResult.success) {
            qDebug() << "配置错误:" << configResult.errorMessage;
        }
    }
    
    // 等待线程完成
    if (thread->isRunning()) {
        thread->wait();
    }
    
    // 清理资源
    try {
        // 尝试关闭日志
        if (scenario != CONFIG_ONLY) {
            qDebug() << "尝试关闭日志...";
            // 这里可能需要添加日志关闭的代码
        }
        
        // 尝试清理配置
        if (scenario != LOG_ONLY) {
            qDebug() << "尝试清理配置...";
            ConfigManager::instance().clearConfig();
        }
    } catch (...) {
        qDebug() << "清理资源时发生异常";
    }
    
    // 处理事件循环，确保信号被处理
    QCoreApplication::processEvents();
    
    // 在测试场景之间添加延迟，确保资源被正确释放
    QThread::msleep(1000);
    
    qDebug() << "========== 测试场景结束 ==========\n";
}

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "配置管理模块和日志模块集成测试";
    qDebug() << "================================";
    
    // 设置全局超时，防止程序卡住
    QTimer::singleShot(30000, &app, [&app]() {
        qDebug() << "全局超时，强制退出程序";
        app.quit();
    });
    
    // 运行各种测试场景
    runTestScenario(CONFIG_ONLY, "仅初始化配置模块");
    runTestScenario(LOG_ONLY, "仅初始化日志模块");
    runTestScenario(CONFIG_FIRST_LOG_SECOND, "先配置后日志");
    runTestScenario(LOG_FIRST_CONFIG_SECOND, "先日志后配置");
    runTestScenario(PARALLEL_INIT, "并行初始化");
    
    qDebug() << "所有测试场景完成";
    
    return 0;
} 