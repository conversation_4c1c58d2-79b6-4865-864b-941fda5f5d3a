#include <QCoreApplication>
#include <QCommandLineParser>
#include <QDebug>
#include <QDir>
#include <QDateTime>
#include <QThread>

#include "infrastructure/file/file_manager.h"
#include "infrastructure/file/file_operations.h"
#include "infrastructure/file/directory_watcher.h"
#include "infrastructure/file/file_type_detector.h"
#include "infrastructure/logging/logger.h"
#include "infrastructure/exception/file_exception.h"

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    app.setApplicationName("TestFileManager");
    app.setApplicationVersion("1.0");
    
    // 命令行参数解析
    QCommandLineParser parser;
    parser.setApplicationDescription("Test application for FileManager module");
    parser.addHelpOption();
    parser.addVersionOption();
    
    QCommandLineOption workDirOption(QStringList() << "w" << "workdir", "Working directory", "workdir", "data");
    parser.addOption(workDirOption);
    
    QCommandLineOption tempDirOption(QStringList() << "t" << "tempdir", "Temporary directory", "tempdir", "temp");
    parser.addOption(tempDirOption);
    
    parser.process(app);
    
    QString workDir = parser.value(workDirOption);
    QString tempDir = parser.value(tempDirOption);
    
    // 初始化日志模块
    Logger& logger = Logger::instance();
    bool logInitResult = logger.init("logs/test_file_manager.log", LogLevel::DEBUG, true, true);
    if (!logInitResult) {
        qCritical() << "Failed to initialize logger";
        return 1;
    }
    
    LOG_INFO("Starting FileManager test application");
    
    try {
        // 初始化文件管理器
        FileManager& fileManager = FileManager::instance();
        bool initResult = fileManager.init(workDir, tempDir, true);
        if (!initResult) {
            LOG_ERROR("Failed to initialize FileManager");
            return 1;
        }
        
        LOG_INFO(QString("FileManager initialized with workDir: %1, tempDir: %2").arg(workDir).arg(tempDir));
        
        // 测试文本文件读写
        LOG_INFO("Testing text file read/write...");
        QString testFilePath = workDir + "/test.txt";
        QString testContent = "This is a test file.\nCreated at " + QDateTime::currentDateTime().toString();
        
        bool writeResult = fileManager.writeTextFile(testFilePath, testContent);
        if (writeResult) {
            LOG_INFO(QString("Successfully wrote to file: %1").arg(testFilePath));
            
            QString readContent;
            bool readResult = fileManager.readTextFile(testFilePath, readContent);
            if (readResult) {
                LOG_INFO(QString("Successfully read from file: %1").arg(testFilePath));
                LOG_INFO(QString("File content: %1").arg(readContent));
                
                if (readContent == testContent) {
                    LOG_INFO("Read content matches written content");
                } else {
                    LOG_ERROR("Read content does not match written content");
                }
            } else {
                LOG_ERROR(QString("Failed to read from file: %1").arg(testFilePath));
            }
        } else {
            LOG_ERROR(QString("Failed to write to file: %1").arg(testFilePath));
        }
        
        // 测试目录操作
        LOG_INFO("Testing directory operations...");
        QString testDirPath = workDir + "/testdir";
        
        bool createDirResult = fileManager.createDirectory(testDirPath);
        if (createDirResult) {
            LOG_INFO(QString("Successfully created directory: %1").arg(testDirPath));
            
            // 在测试目录中创建文件
            QString testFileInDir = testDirPath + "/test_in_dir.txt";
            bool writeInDirResult = fileManager.writeTextFile(testFileInDir, "This is a test file in directory");
            if (writeInDirResult) {
                LOG_INFO(QString("Successfully wrote to file in directory: %1").arg(testFileInDir));
            } else {
                LOG_ERROR(QString("Failed to write to file in directory: %1").arg(testFileInDir));
            }
            
            // 列出目录内容
            QStringList entries;
            bool listResult = fileManager.listDirectory(testDirPath, entries);
            if (listResult) {
                LOG_INFO(QString("Directory %1 contains:").arg(testDirPath));
                for (const QString& entry : entries) {
                    LOG_INFO(QString("  %1").arg(entry));
                }
            } else {
                LOG_ERROR(QString("Failed to list directory: %1").arg(testDirPath));
            }
        } else {
            LOG_ERROR(QString("Failed to create directory: %1").arg(testDirPath));
        }
        
        // 测试文件复制和移动
        LOG_INFO("Testing file copy and move...");
        QString copyFilePath = workDir + "/test_copy.txt";
        bool copyResult = fileManager.copyFile(testFilePath, copyFilePath);
        if (copyResult) {
            LOG_INFO(QString("Successfully copied file from %1 to %2").arg(testFilePath).arg(copyFilePath));
            
            QString moveFilePath = workDir + "/test_moved.txt";
            bool moveResult = fileManager.moveFile(copyFilePath, moveFilePath);
            if (moveResult) {
                LOG_INFO(QString("Successfully moved file from %1 to %2").arg(copyFilePath).arg(moveFilePath));
                
                // 检查原文件是否不存在
                if (!fileManager.exists(copyFilePath)) {
                    LOG_INFO(QString("Original file %1 no longer exists after move").arg(copyFilePath));
                } else {
                    LOG_ERROR(QString("Original file %1 still exists after move").arg(copyFilePath));
                }
                
                // 删除移动后的文件
                bool deleteResult = fileManager.deleteFile(moveFilePath);
                if (deleteResult) {
                    LOG_INFO(QString("Successfully deleted file: %1").arg(moveFilePath));
                } else {
                    LOG_ERROR(QString("Failed to delete file: %1").arg(moveFilePath));
                }
            } else {
                LOG_ERROR(QString("Failed to move file from %1 to %2").arg(copyFilePath).arg(moveFilePath));
            }
        } else {
            LOG_ERROR(QString("Failed to copy file from %1 to %2").arg(testFilePath).arg(copyFilePath));
        }
        
        // 测试文件监视
        LOG_INFO("Testing directory watcher...");
        DirectoryWatcher* watcher = fileManager.createDirectoryWatcher(workDir);
        
        QObject::connect(watcher, &DirectoryWatcher::fileCreated, [](const QString& path) {
            LOG_INFO(QString("File created: %1").arg(path));
        });
        
        QObject::connect(watcher, &DirectoryWatcher::fileModified, [](const QString& path) {
            LOG_INFO(QString("File modified: %1").arg(path));
        });
        
        QObject::connect(watcher, &DirectoryWatcher::fileDeleted, [](const QString& path) {
            LOG_INFO(QString("File deleted: %1").arg(path));
        });
        
        watcher->start();
        LOG_INFO(QString("Started watching directory: %1").arg(workDir));
        
        // 创建、修改和删除文件以触发监视器事件
        LOG_INFO("Creating, modifying and deleting files to trigger watcher events...");
        QString watcherTestFile = workDir + "/watcher_test.txt";
        fileManager.writeTextFile(watcherTestFile, "Initial content");
        
        // 等待一段时间以确保事件被处理
        QThread::sleep(1);
        
        fileManager.writeTextFile(watcherTestFile, "Modified content", false);
        
        // 等待一段时间以确保事件被处理
        QThread::sleep(1);
        
        fileManager.deleteFile(watcherTestFile);
        
        // 等待一段时间以确保事件被处理
        QThread::sleep(1);
        
        watcher->stop();
        fileManager.destroyDirectoryWatcher(watcher);
        LOG_INFO("Stopped directory watcher");
        
        // 测试临时文件和目录
        LOG_INFO("Testing temporary files and directories...");
        QString tempFilePath;
        bool createTempFileResult = fileManager.createTempFile("test_", ".tmp", tempFilePath);
        if (createTempFileResult) {
            LOG_INFO(QString("Successfully created temporary file: %1").arg(tempFilePath));
            
            fileManager.writeTextFile(tempFilePath, "This is a temporary file");
            
            QString tempDirPath;
            bool createTempDirResult = fileManager.createTempDirectory("test_dir_", tempDirPath);
            if (createTempDirResult) {
                LOG_INFO(QString("Successfully created temporary directory: %1").arg(tempDirPath));
                
                // 清理临时文件
                bool cleanupResult = fileManager.cleanupTempFiles(0); // 清理所有临时文件
                if (cleanupResult) {
                    LOG_INFO("Successfully cleaned up temporary files");
                    
                    // 检查临时文件和目录是否已被删除
                    if (!fileManager.exists(tempFilePath) && !fileManager.exists(tempDirPath)) {
                        LOG_INFO("Temporary file and directory were successfully deleted");
                    } else {
                        LOG_ERROR("Temporary file or directory still exists after cleanup");
                    }
                } else {
                    LOG_ERROR("Failed to clean up temporary files");
                }
            } else {
                LOG_ERROR(QString("Failed to create temporary directory with prefix: test_dir_"));
            }
        } else {
            LOG_ERROR(QString("Failed to create temporary file with prefix: test_ and suffix: .tmp"));
        }
        
        // 测试文件类型检测
        LOG_INFO("Testing file type detection...");
        FileType textFileType = FileType::TEXT; // 直接使用 TEXT 类型，避免调用 detectFileType
        LOG_INFO(QString("Text file type: %1").arg(FileTypeDetector::typeToString(textFileType)));
        
        // 测试文件备份和恢复
        LOG_INFO("Testing file backup and restore...");
        QString backupPath;
        bool backupResult = fileManager.backupFile(testFilePath, backupPath);
        if (backupResult) {
            LOG_INFO(QString("Successfully backed up file from %1 to %2").arg(testFilePath).arg(backupPath));
            
            // 修改原文件
            fileManager.writeTextFile(testFilePath, "This file has been modified after backup");
            
            // 恢复备份
            bool restoreResult = fileManager.restoreFile(backupPath, testFilePath);
            if (restoreResult) {
                LOG_INFO(QString("Successfully restored file from %1 to %2").arg(backupPath).arg(testFilePath));
                
                // 检查恢复后的内容
                QString restoredContent;
                fileManager.readTextFile(testFilePath, restoredContent);
                LOG_INFO(QString("Restored file content: %1").arg(restoredContent));
            } else {
                LOG_ERROR(QString("Failed to restore file from %1 to %2").arg(backupPath).arg(testFilePath));
            }
            
            // 删除备份文件
            fileManager.deleteFile(backupPath);
        } else {
            LOG_ERROR(QString("Failed to backup file from %1").arg(testFilePath));
        }
        
        // 清理测试文件和目录
        LOG_INFO("Cleaning up test files and directories...");
        fileManager.deleteFile(testFilePath);
        
        // 递归删除测试目录
        fileManager.deleteDirectory(testDirPath, true);
        
        LOG_INFO("FileManager test completed successfully");
        
    } catch (const FileException& e) {
        LOG_ERROR(QString("File exception: %1").arg(e.getFormattedMessage()));
        return 1;
    } catch (const std::exception& e) {
        LOG_ERROR(QString("Standard exception: %1").arg(e.what()));
        return 1;
    } catch (...) {
        LOG_ERROR("Unknown exception");
        return 1;
    }
    
    return 0;
} 