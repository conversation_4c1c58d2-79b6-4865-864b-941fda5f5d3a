#include <QCoreApplication>
#include <QCommandLineParser>
#include <QDebug>
#include <QThread>
#include <QElapsedTimer>
#include <QMutex>
#include <QDir>
#include <QFile>
#include <QProcess>
#include <QDateTime>
#include <QTextStream>
#include <QTimer>
#include <functional>
#include <future>
#include <chrono>
#include <iostream>
#include <signal.h>

#include "core/camera/camera_manager.h"
#include "core/camera/camera.h"
#include "core/camera/usb_camera.h"
#include "infrastructure/logging/logger.h"
#include "infrastructure/exception/app_exception.h"
#include "infrastructure/exception/camera_exception.h"

using namespace SmartScope;
using namespace Core;
using namespace SmartScope::Infrastructure;

volatile sig_atomic_t g_running = 1;

void signalHandler(int sig) {
    LOG_INFO(QString("接收到信号 %1，准备退出").arg(sig));
    g_running = 0;
}

void initLogger() {
    // 设置日志级别
    Logger::instance().setLogLevel(LogLevel::DEBUG);  // 改为DEBUG级别以获取更多信息
    
    // 启用控制台输出
    Logger::instance().setConsoleEnabled(true);
    
    // 设置日志文件
    QString logDir = "logs";
    QDir dir;
    if (!dir.exists(logDir)) {
        dir.mkdir(logDir);
    }
    
    QString logFileName = QString("%1/camera_sync_test_%2.log")
        .arg(logDir)
        .arg(QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss"));
    
    Logger::instance().setFileEnabled(true);
    Logger::instance().setLogFilePath(logFileName);
    
    LOG_INFO("日志系统初始化完成");
}

// 使用超时运行函数
template<typename Func, typename... Args>
bool runWithTimeout(const QString& operationName, int timeoutMs, Func&& func, Args&&... args)
{
    LOG_DEBUG(QString("开始执行操作: %1，超时时间: %2毫秒").arg(operationName).arg(timeoutMs));
    
    std::packaged_task<void()> task(std::bind(std::forward<Func>(func), std::forward<Args>(args)...));
    std::future<void> future = task.get_future();
    
    std::thread t(std::move(task));
    t.detach();
    
    std::future_status status = future.wait_for(std::chrono::milliseconds(timeoutMs));
    
    if (status == std::future_status::timeout) {
        LOG_WARNING(QString("操作超时: %1（%2毫秒）").arg(operationName).arg(timeoutMs));
        return false;
    } else if (status == std::future_status::ready) {
        try {
            future.get(); // 获取结果，如果有异常会抛出
            LOG_DEBUG(QString("操作完成: %1").arg(operationName));
            return true;
        } catch (const std::exception& e) {
            LOG_ERROR(QString("操作异常: %1，错误: %2").arg(operationName).arg(e.what()));
            return false;
        }
    }
    
    return false;
}

// 测试相机同步功能
void testCameraSync(CameraManager& cameraManager, SyncStrategy strategy, int durationSeconds, const QString& leftCameraId, const QString& rightCameraId, int timeoutMs) {
    LOG_INFO(QString("开始测试相机同步，策略: %1, 持续时间: %2秒")
        .arg(static_cast<int>(strategy))
        .arg(durationSeconds));
    
    LOG_INFO(QString("使用左相机: %1").arg(leftCameraId));
    LOG_INFO(QString("使用右相机: %1").arg(rightCameraId));
    
    // 设置同步策略
    cameraManager.setSyncStrategy(strategy);
    
    // 帧计数器
    int leftFrameCount = 0;
    int rightFrameCount = 0;
    int syncFrameCount = 0;
    
    // 连接信号
    QObject::connect(&cameraManager, &CameraManager::newSyncFrame,
        [&syncFrameCount](const SyncFrame& frame) {
            syncFrameCount++;
            if (syncFrameCount % 30 == 0) {
                LOG_INFO(QString("收到同步帧 #%1, 左相机时间戳: %2, 右相机时间戳: %3, 时差: %4ms")
                    .arg(syncFrameCount)
                    .arg(frame.leftTimestamp)
                    .arg(frame.rightTimestamp)
                    .arg(qAbs(frame.leftTimestamp - frame.rightTimestamp)));
            }
        });
    
    // 连接左相机信号
    Camera* leftCamera = cameraManager.getLeftCamera();
    if (leftCamera) {
        QObject::connect(leftCamera, &Camera::newFrame,
            [&leftFrameCount](const QImage& frame) {
                leftFrameCount++;
                if (leftFrameCount % 30 == 0) {
                    LOG_INFO(QString("左相机帧 #%1").arg(leftFrameCount));
                }
            });
    }
    
    // 连接右相机信号
    Camera* rightCamera = cameraManager.getRightCamera();
    if (rightCamera) {
        QObject::connect(rightCamera, &Camera::newFrame,
            [&rightFrameCount](const QImage& frame) {
                rightFrameCount++;
                if (rightFrameCount % 30 == 0) {
                    LOG_INFO(QString("右相机帧 #%1").arg(rightFrameCount));
                }
            });
    }
    
    // 启动相机
    try {
        // 刷新相机列表
        LOG_DEBUG("开始刷新相机列表");
        if (!cameraManager.refreshCameraList()) {
            LOG_ERROR("刷新相机列表失败");
            return;
        }
        
        // 获取相机列表
        QStringList cameraList = cameraManager.getAvailableCameras();
        LOG_INFO(QString("发现 %1 个相机:").arg(cameraList.size()));
        for (const QString& cameraId : cameraList) {
            LOG_INFO(QString("  - %1").arg(cameraId));
        }
        
        // 检查是否有足够的相机
        if (cameraList.size() < 2) {
            LOG_ERROR("需要至少两个相机进行同步测试");
            return;
        }
        
        // 检查指定的相机是否在可用列表中
        if (!cameraList.contains(leftCameraId)) {
            LOG_ERROR(QString("左相机 %1 不在可用列表中").arg(leftCameraId));
            LOG_INFO("可用相机列表:");
            for (const QString& cameraId : cameraList) {
                LOG_INFO(QString("  - %1").arg(cameraId));
            }
            return;
        }
        
        if (!cameraList.contains(rightCameraId)) {
            LOG_ERROR(QString("右相机 %1 不在可用列表中").arg(rightCameraId));
            LOG_INFO("可用相机列表:");
            for (const QString& cameraId : cameraList) {
                LOG_INFO(QString("  - %1").arg(cameraId));
            }
            return;
        }
        
        // 检查相机是否可访问
        LOG_DEBUG(QString("检查左相机是否可访问: %1").arg(leftCameraId));
        if (!cameraManager.isCameraAccessible(leftCameraId)) {
            LOG_ERROR(QString("左相机不可访问: %1").arg(leftCameraId));
            return;
        }
        
        LOG_DEBUG(QString("检查右相机是否可访问: %1").arg(rightCameraId));
        if (!cameraManager.isCameraAccessible(rightCameraId)) {
            LOG_ERROR(QString("右相机不可访问: %1").arg(rightCameraId));
            return;
        }
        
        // 检查相机是否支持视频捕获
        LOG_DEBUG(QString("检查左相机是否支持视频捕获: %1").arg(leftCameraId));
        if (!cameraManager.isVideoCaptureSupported(leftCameraId)) {
            LOG_ERROR(QString("左相机不支持视频捕获: %1").arg(leftCameraId));
            return;
        }
        
        LOG_DEBUG(QString("检查右相机是否支持视频捕获: %1").arg(rightCameraId));
        if (!cameraManager.isVideoCaptureSupported(rightCameraId)) {
            LOG_ERROR(QString("右相机不支持视频捕获: %1").arg(rightCameraId));
            return;
        }
        
        // 使用超时机制设置左相机
        LOG_DEBUG(QString("开始设置左相机: %1").arg(leftCameraId));
        bool leftCameraSet = runWithTimeout("设置左相机", timeoutMs, [&cameraManager, &leftCameraId]() {
            if (!cameraManager.setLeftCamera(leftCameraId)) {
                throw std::runtime_error("设置左相机失败");
            }
        });
        
        if (!leftCameraSet) {
            LOG_ERROR("设置左相机超时或失败");
            return;
        }
        
        // 使用超时机制设置右相机
        LOG_DEBUG(QString("开始设置右相机: %1").arg(rightCameraId));
        bool rightCameraSet = runWithTimeout("设置右相机", timeoutMs, [&cameraManager, &rightCameraId]() {
            if (!cameraManager.setRightCamera(rightCameraId)) {
                throw std::runtime_error("设置右相机失败");
            }
        });
        
        if (!rightCameraSet) {
            LOG_ERROR("设置右相机超时或失败");
            cameraManager.closeCamera(leftCameraId);
            return;
        }
        
        // 使用超时机制打开左相机
        LOG_DEBUG(QString("开始打开左相机: %1").arg(leftCameraId));
        bool leftCameraOpened = runWithTimeout("打开左相机", 5000, [&cameraManager, &leftCameraId]() {
            if (!cameraManager.openCamera(leftCameraId)) {
                throw std::runtime_error("打开左相机失败");
            }
        });
        
        if (!leftCameraOpened) {
            LOG_ERROR("打开左相机超时或失败");
            return;
        }
        
        // 使用超时机制打开右相机
        LOG_DEBUG(QString("开始打开右相机: %1").arg(rightCameraId));
        bool rightCameraOpened = runWithTimeout("打开右相机", 5000, [&cameraManager, &rightCameraId]() {
            if (!cameraManager.openCamera(rightCameraId)) {
                throw std::runtime_error("打开右相机失败");
            }
        });
        
        if (!rightCameraOpened) {
            LOG_ERROR("打开右相机超时或失败");
            cameraManager.closeCamera(leftCameraId);
            return;
        }
        
        // 启用同步
        LOG_DEBUG("开始启用同步");
        cameraManager.enableSync(true);
        
        // 设置同步时间阈值（33毫秒，约30fps）
        LOG_DEBUG("设置同步时间阈值: 33毫秒");
        cameraManager.setSyncTimeThreshold(33);
        
        // 设置帧缓冲区大小
        LOG_DEBUG("设置帧缓冲区大小: 10");
        cameraManager.setFrameBufferSize(10);
        
        // 开始采集
        LOG_DEBUG("开始采集");
        bool captureStarted = runWithTimeout("开始采集", 5000, [&cameraManager]() {
            if (!cameraManager.startCapture()) {
                throw std::runtime_error("开始采集失败");
            }
        });
        
        if (!captureStarted) {
            LOG_ERROR("开始采集超时或失败");
            cameraManager.closeCamera(leftCameraId);
            cameraManager.closeCamera(rightCameraId);
            return;
        }
        
        // 运行指定时间
        LOG_INFO(QString("相机同步已启动，将运行 %1 秒").arg(durationSeconds));
        QElapsedTimer timer;
        timer.start();
        
        while (timer.elapsed() < durationSeconds * 1000 && g_running) {
            QThread::msleep(100);
            
            // 每秒输出一次状态信息
            if (timer.elapsed() / 1000 != (timer.elapsed() - 100) / 1000) {
                LOG_INFO(QString("测试运行中 - 已运行: %1 秒，同步帧: %2，左相机帧: %3，右相机帧: %4")
                    .arg(timer.elapsed() / 1000)
                    .arg(syncFrameCount)
                    .arg(leftFrameCount)
                    .arg(rightFrameCount));
            }
        }
        
        // 停止采集
        LOG_DEBUG("停止采集");
        cameraManager.stopCapture();
        
        // 禁用同步
        LOG_DEBUG("禁用同步");
        cameraManager.enableSync(false);
        
        // 关闭相机
        LOG_DEBUG(QString("关闭左相机: %1").arg(leftCameraId));
        cameraManager.closeCamera(leftCameraId);
        
        LOG_DEBUG(QString("关闭右相机: %1").arg(rightCameraId));
        cameraManager.closeCamera(rightCameraId);
        
        // 输出统计信息
        LOG_INFO(QString("测试完成，左相机帧数: %1, 右相机帧数: %2, 同步帧数: %3")
            .arg(leftFrameCount)
            .arg(rightFrameCount)
            .arg(syncFrameCount));
        
        // 计算同步率
        double syncRate = (syncFrameCount > 0 && (leftFrameCount > 0 || rightFrameCount > 0)) ?
            static_cast<double>(syncFrameCount) / qMax(leftFrameCount, rightFrameCount) * 100.0 : 0.0;
        
        LOG_INFO(QString("同步率: %1%").arg(syncRate, 0, 'f', 2));
        
    } catch (const AppException& e) {
        LOG_ERROR(QString("测试过程中发生应用异常: %1").arg(e.what()));
    } catch (const std::exception& e) {
        LOG_ERROR(QString("测试过程中发生标准异常: %1").arg(e.what()));
    } catch (...) {
        LOG_ERROR("测试过程中发生未知异常");
    }
}

int main(int argc, char *argv[])
{
    // 注册信号处理
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
    
    // 初始化Qt应用
    QCoreApplication app(argc, argv);
    
    // 初始化日志系统
    initLogger();
    
    LOG_INFO("相机同步测试程序启动");
    
    // 设置应用程序信息
    QCoreApplication::setApplicationName("TestCameraSync");
    QCoreApplication::setApplicationVersion("1.0");
    
    // 创建命令行解析器
    QCommandLineParser parser;
    parser.setApplicationDescription("相机同步测试程序");
    parser.addHelpOption();
    parser.addVersionOption();
    
    // 添加同步策略选项
    QCommandLineOption syncStrategyOption(QStringList() << "s" << "sync-strategy",
        "同步策略 (0=None, 1=SoftwareTrigger, 2=TimestampMatch, 3=Combined)",
        "strategy", "3");
    parser.addOption(syncStrategyOption);
    
    // 添加测试持续时间选项
    QCommandLineOption durationOption(QStringList() << "d" << "duration",
        "测试持续时间（秒）",
        "seconds", "5");
    parser.addOption(durationOption);
    
    // 添加超时选项
    QCommandLineOption timeoutOption(QStringList() << "t" << "timeout",
        "操作超时时间（毫秒）",
        "milliseconds", "3000");
    parser.addOption(timeoutOption);
    
    // 添加左相机设备选项
    QCommandLineOption leftCameraOption(QStringList() << "l" << "left-camera",
        "左相机设备路径",
        "device", "/dev/video1");
    parser.addOption(leftCameraOption);
    
    // 添加右相机设备选项
    QCommandLineOption rightCameraOption(QStringList() << "r" << "right-camera",
        "右相机设备路径",
        "device", "/dev/video3");
    parser.addOption(rightCameraOption);
    
    // 解析命令行参数
    parser.process(app);
    
    // 获取同步策略
    bool ok;
    int strategyValue = parser.value(syncStrategyOption).toInt(&ok);
    if (!ok || strategyValue < 0 || strategyValue > 3) {
        LOG_ERROR("无效的同步策略参数，使用默认值(3)");
        strategyValue = 3;
    }
    SyncStrategy strategy = static_cast<SyncStrategy>(strategyValue);
    
    // 获取测试持续时间
    int durationSeconds = parser.value(durationOption).toInt(&ok);
    if (!ok || durationSeconds <= 0) {
        LOG_ERROR("无效的测试时长参数，使用默认值(5)");
        durationSeconds = 5;
    }
    
    // 获取超时时间
    int timeoutMs = parser.value(timeoutOption).toInt(&ok);
    if (!ok || timeoutMs <= 0) {
        LOG_ERROR("无效的超时参数，使用默认值(3000)");
        timeoutMs = 3000;
    }
    
    // 获取左右相机设备路径
    QString leftCameraId = parser.value(leftCameraOption);
    QString rightCameraId = parser.value(rightCameraOption);
    
    // 记录参数
    LOG_INFO(QString("同步策略: %1").arg(strategyValue));
    LOG_INFO(QString("测试持续时间: %1秒").arg(durationSeconds));
    LOG_INFO(QString("操作超时时间: %1毫秒").arg(timeoutMs));
    LOG_INFO(QString("左相机设备: %1").arg(leftCameraId));
    LOG_INFO(QString("右相机设备: %1").arg(rightCameraId));
    
    // 设置应用程序超时退出
    QTimer::singleShot(durationSeconds * 1000 + timeoutMs * 2, &app, [&app]() {
        LOG_WARNING("程序超时，强制退出");
        app.quit();
    });
    
    // 初始化相机管理器
    CameraManager& cameraManager = CameraManager::instance();
    
    try {
        // 使用超时机制初始化相机管理器
        LOG_DEBUG("开始初始化相机管理器");
        bool initialized = runWithTimeout("初始化相机管理器", timeoutMs, [&cameraManager]() {
            if (!cameraManager.initialize()) {
                throw std::runtime_error("初始化相机管理器失败");
            }
        });
        
        if (!initialized) {
            LOG_ERROR("初始化相机管理器超时");
            return 1;
        }
        
        // 测试相机同步
        testCameraSync(cameraManager, strategy, durationSeconds, leftCameraId, rightCameraId, timeoutMs);
        
        // 关闭相机管理器
        LOG_DEBUG("开始关闭相机管理器");
        cameraManager.shutdown();
        
        LOG_INFO("相机同步测试程序正常退出");
        return 0;
    } catch (const AppException& e) {
        LOG_ERROR(QString("应用程序异常: %1, 文件: %2, 行: %3, 函数: %4")
            .arg(e.what())
            .arg(e.getFile())
            .arg(e.getLine())
            .arg(e.getFunction()));
    } catch (const std::exception& e) {
        LOG_ERROR(QString("标准异常: %1").arg(e.what()));
    } catch (...) {
        LOG_ERROR("未知异常");
    }
    
    // 关闭相机管理器
    try {
        cameraManager.shutdown();
    } catch (...) {
        LOG_ERROR("关闭相机管理器时发生异常");
    }
    
    return 1;
}

#include "test_camera_sync.moc" 