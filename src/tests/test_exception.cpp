#include <QCoreApplication>
#include <QDebug>
#include <QThread>
#include <QCommandLineParser>
#include <QDateTime>

#include "infrastructure/exception/app_exception.h"
#include "infrastructure/exception/config_exception.h"
#include "infrastructure/exception/file_exception.h"
#include "infrastructure/exception/network_exception.h"
#include "infrastructure/exception/business_exception.h"
#include "infrastructure/exception/exception_handler.h"
#include "infrastructure/logging/logger.h"

// 测试函数：抛出 AppException
void testAppException() {
    qDebug() << "测试 AppException...";
    THROW_APP_EXCEPTION("这是一个应用异常");
}

// 测试函数：抛出 ConfigException
void testConfigException() {
    qDebug() << "测试 ConfigException...";
    THROW_CONFIG_EXCEPTION("这是一个配置异常");
}

// 测试函数：抛出 FileException
void testFileException() {
    qDebug() << "测试 FileException...";
    THROW_FILE_EXCEPTION("这是一个文件异常");
}

// 测试函数：抛出 NetworkException
void testNetworkException() {
    qDebug() << "测试 NetworkException...";
    THROW_NETWORK_EXCEPTION("这是一个网络异常");
}

// 测试函数：抛出 BusinessException
void testBusinessException() {
    qDebug() << "测试 BusinessException...";
    THROW_BUSINESS_EXCEPTION("这是一个业务异常");
}

// 测试函数：抛出 ConfigKeyNotFoundException
void testConfigKeyNotFoundException() {
    qDebug() << "测试 ConfigKeyNotFoundException...";
    THROW_CONFIG_KEY_NOT_FOUND_EXCEPTION("app.name");
}

// 测试函数：抛出 FileNotFoundException
void testFileNotFoundException() {
    qDebug() << "测试 FileNotFoundException...";
    THROW_FILE_NOT_FOUND_EXCEPTION("test.txt");
}

// 测试函数：抛出 std::exception
void testStdException() {
    qDebug() << "测试 std::exception...";
    throw std::runtime_error("这是一个标准异常");
}

// 测试函数：抛出未知异常
void testUnknownException() {
    qDebug() << "测试未知异常...";
    throw 42;
}

// 测试异常处理器
void testExceptionHandler() {
    qDebug() << "\n===== 测试异常处理器 =====";
    
    ExceptionHandler& handler = ExceptionHandler::instance();
    
    // 测试成功的情况
    bool success = handler.handle([]() {
        qDebug() << "这是一个成功的操作";
    }, false);
    qDebug() << "成功的操作结果:" << (success ? "成功" : "失败");
    
    // 测试 AppException
    success = handler.handle(testAppException, false);
    qDebug() << "AppException 处理结果:" << (success ? "成功" : "失败");
    
    // 测试 ConfigException
    success = handler.handle(testConfigException, false);
    qDebug() << "ConfigException 处理结果:" << (success ? "成功" : "失败");
    
    // 测试 FileException
    success = handler.handle(testFileException, false);
    qDebug() << "FileException 处理结果:" << (success ? "成功" : "失败");
    
    // 测试 NetworkException
    success = handler.handle(testNetworkException, false);
    qDebug() << "NetworkException 处理结果:" << (success ? "成功" : "失败");
    
    // 测试 BusinessException
    success = handler.handle(testBusinessException, false);
    qDebug() << "BusinessException 处理结果:" << (success ? "成功" : "失败");
    
    // 测试 std::exception
    success = handler.handle(testStdException, false);
    qDebug() << "std::exception 处理结果:" << (success ? "成功" : "失败");
    
    // 测试未知异常
    success = handler.handle(testUnknownException, false);
    qDebug() << "未知异常处理结果:" << (success ? "成功" : "失败");
}

// 测试直接捕获异常
void testDirectCatch() {
    qDebug() << "\n===== 测试直接捕获异常 =====";
    
    try {
        testAppException();
    } catch (const AppException& e) {
        qDebug() << "捕获到 AppException:";
        qDebug() << "  消息:" << e.getMessage();
        qDebug() << "  文件:" << e.getFile();
        qDebug() << "  行号:" << e.getLine();
        qDebug() << "  函数:" << e.getFunction();
        qDebug() << "  时间戳:" << e.getTimestamp();
        qDebug() << "  格式化消息:" << e.getFormattedMessage();
        qDebug() << "  类型名称:" << e.getTypeName();
    }
    
    try {
        testConfigKeyNotFoundException();
    } catch (const ConfigKeyNotFoundException& e) {
        qDebug() << "捕获到 ConfigKeyNotFoundException:";
        qDebug() << "  消息:" << e.getMessage();
        qDebug() << "  键:" << e.getKey();
        qDebug() << "  类型名称:" << e.getTypeName();
    }
    
    try {
        testFileNotFoundException();
    } catch (const FileNotFoundException& e) {
        qDebug() << "捕获到 FileNotFoundException:";
        qDebug() << "  消息:" << e.getMessage();
        qDebug() << "  文件路径:" << e.getFilePath();
        qDebug() << "  类型名称:" << e.getTypeName();
    }
}

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    app.setApplicationName("TestException");
    app.setApplicationVersion("1.0.0");
    
    QCommandLineParser parser;
    parser.setApplicationDescription("异常处理模块测试程序");
    parser.addHelpOption();
    parser.addVersionOption();
    parser.process(app);
    
    qDebug() << "异常处理模块测试程序启动";
    
    // 初始化日志模块
    Logger& logger = Logger::instance();
    bool logInitResult = logger.init("", LogLevel::DEBUG, true, false);
    qDebug() << "日志模块初始化" << (logInitResult ? "成功" : "失败");
    
    // 测试各种异常
    qDebug() << "\n===== 测试各种异常类型 =====";
    
    try {
        testAppException();
    } catch (const AppException& e) {
        qDebug() << "捕获到异常:" << e.what();
    }
    
    try {
        testConfigException();
    } catch (const ConfigException& e) {
        qDebug() << "捕获到异常:" << e.what();
    }
    
    try {
        testFileException();
    } catch (const FileException& e) {
        qDebug() << "捕获到异常:" << e.what();
    }
    
    try {
        testNetworkException();
    } catch (const NetworkException& e) {
        qDebug() << "捕获到异常:" << e.what();
    }
    
    try {
        testBusinessException();
    } catch (const BusinessException& e) {
        qDebug() << "捕获到异常:" << e.what();
    }
    
    // 测试异常处理器
    testExceptionHandler();
    
    // 测试直接捕获异常
    testDirectCatch();
    
    qDebug() << "\n异常处理模块测试程序结束";
    
    return 0;
} 