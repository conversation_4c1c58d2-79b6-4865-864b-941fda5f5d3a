/**
 * @file test_multi_camera.cpp
 * @brief 多相机管理器测试程序
 */

#include "core/camera/multi_camera_manager.h"
#include <iostream>
#include <string>
#include <thread>
#include <chrono>
#include <signal.h>
#include <opencv2/opencv.hpp>

// 更新命名空间引用
using namespace SmartScope::Core::CameraUtils;

// 全局变量
std::atomic<bool> running(true);

// 信号处理函数
void signalHandler(int signum) {
    std::cout << "接收到信号 " << signum << "，准备退出" << std::endl;
    running = false;
}

int main(int argc, char** argv) {
    // 注册信号处理函数
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);

    // 默认参数
    std::string leftCameraId = "/dev/video1";
    std::string rightCameraId = "/dev/video3";
    int durationSeconds = 30;
    bool showUI = true;
    SyncMode syncMode = SyncMode::LOW_LATENCY;

    // 解析命令行参数
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];
        if (arg == "--left-camera" && i + 1 < argc) {
            leftCameraId = argv[++i];
        } else if (arg == "--right-camera" && i + 1 < argc) {
            rightCameraId = argv[++i];
        } else if (arg == "--duration" && i + 1 < argc) {
            durationSeconds = std::stoi(argv[++i]);
        } else if (arg == "--no-ui") {
            showUI = false;
        } else if (arg == "--sync-mode" && i + 1 < argc) {
            std::string mode = argv[++i];
            if (mode == "sync") {
                syncMode = SyncMode::SYNC;
            } else if (mode == "low-latency") {
                syncMode = SyncMode::LOW_LATENCY;
            } else if (mode == "no-sync") {
                syncMode = SyncMode::NO_SYNC;
            }
        } else if (arg == "--help") {
            std::cout << "用法: " << argv[0] << " [选项]" << std::endl;
            std::cout << "选项:" << std::endl;
            std::cout << "  --left-camera <设备>    左相机设备路径 (默认: /dev/video1)" << std::endl;
            std::cout << "  --right-camera <设备>   右相机设备路径 (默认: /dev/video3)" << std::endl;
            std::cout << "  --duration <秒>         测试持续时间 (默认: 30秒)" << std::endl;
            std::cout << "  --no-ui                 不显示UI界面 (提高性能)" << std::endl;
            std::cout << "  --sync-mode <模式>      同步模式 (sync/low-latency/no-sync, 默认: low-latency)" << std::endl;
            std::cout << "  --help                  显示此帮助信息" << std::endl;
            return 0;
        }
    }

    std::cout << "多相机管理器测试程序" << std::endl;
    std::cout << "左相机: " << leftCameraId << std::endl;
    std::cout << "右相机: " << rightCameraId << std::endl;
    std::cout << "测试时长: " << durationSeconds << " 秒" << std::endl;
    std::cout << "显示UI: " << (showUI ? "是" : "否") << std::endl;
    std::cout << "同步模式: " << (syncMode == SyncMode::SYNC ? "严格同步" : 
                               (syncMode == SyncMode::LOW_LATENCY ? "低延迟" : "完全不同步")) << std::endl;

    // 创建多相机管理器
    MultiCameraManager cameraManager;
    
    // 设置同步模式
    cameraManager.setSyncMode(syncMode);
    
    // 添加相机
    CameraConfig leftConfig(640, 480, 30, 1);
    CameraConfig rightConfig(640, 480, 30, 1);
    
    cameraManager.addCamera(leftCameraId, "左相机", leftConfig);
    cameraManager.addCamera(rightCameraId, "右相机", rightConfig);
    
    // 创建窗口 - 只在需要显示UI时创建
    if (showUI) {
        cv::namedWindow("左相机", cv::WINDOW_NORMAL);
        cv::namedWindow("右相机", cv::WINDOW_NORMAL);
        cv::resizeWindow("左相机", 640, 480);
        cv::resizeWindow("右相机", 640, 480);
    }
    
    // 设置同步帧回调函数
    cameraManager.setSyncFrameCallback([&](const std::map<std::string, cv::Mat>& frames, 
                                          const std::map<std::string, int64_t>& timestamps) {
        // 只在需要显示UI时处理显示逻辑
        if (showUI) {
            // 显示每个相机的帧
            for (const auto& frame : frames) {
                const std::string& cameraId = frame.first;
                const cv::Mat& image = frame.second;
                
                // 获取时间戳
                int64_t timestamp = timestamps.at(cameraId);
                
                // 创建显示帧
                cv::Mat displayFrame = image.clone();
                
                // 添加时间戳
                std::string timestampStr = std::to_string(timestamp);
                cv::putText(displayFrame, timestampStr, cv::Point(10, 30), 
                            cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(0, 255, 0), 1);
                
                // 显示帧
                if (cameraId == leftCameraId) {
                    cv::imshow("左相机", displayFrame);
                } else if (cameraId == rightCameraId) {
                    cv::imshow("右相机", displayFrame);
                }
            }
            
            // 处理按键
            int key = cv::waitKey(1);
            if (key == 27) { // ESC键
                std::cout << "用户按下ESC键，退出" << std::endl;
                running = false;
            }
        }
    });
    
    // 启动相机
    if (!cameraManager.startAll()) {
        std::cerr << "启动相机失败" << std::endl;
        return 1;
    }
    
    // 计时器
    auto startTime = std::chrono::steady_clock::now();
    auto lastStatusTime = startTime;
    
    // 主循环
    while (running) {
        // 检查是否达到测试时长
        auto currentTime = std::chrono::steady_clock::now();
        auto elapsedSeconds = std::chrono::duration_cast<std::chrono::seconds>(currentTime - startTime).count();
        
        if (elapsedSeconds >= durationSeconds) {
            std::cout << "测试时间到，退出" << std::endl;
            break;
        }
        
        // 每秒显示状态信息
        auto statusElapsed = std::chrono::duration_cast<std::chrono::seconds>(currentTime - lastStatusTime).count();
        if (statusElapsed >= 1) {
            // 获取相机信息
            auto leftInfo = cameraManager.getCameraInfo(leftCameraId);
            auto rightInfo = cameraManager.getCameraInfo(rightCameraId);
            
            std::cout << "已运行: " << elapsedSeconds << " 秒, 左相机帧: " << leftInfo.frameCount 
                      << ", 右相机帧: " << rightInfo.frameCount 
                      << ", 同步帧: " << cameraManager.getSyncFrameCount()
                      << ", 丢失同步: " << cameraManager.getMissedSyncCount() << std::endl;
            
            lastStatusTime = currentTime;
        }
        
        // 短暂休眠，减少CPU使用
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    // 停止相机
    cameraManager.stopAll();
    
    // 销毁窗口
    if (showUI) {
        cv::destroyAllWindows();
    }
    
    // 输出统计信息
    auto leftInfo = cameraManager.getCameraInfo(leftCameraId);
    auto rightInfo = cameraManager.getCameraInfo(rightCameraId);
    
    std::cout << "测试完成" << std::endl;
    std::cout << "左相机总帧数: " << leftInfo.frameCount << std::endl;
    std::cout << "右相机总帧数: " << rightInfo.frameCount << std::endl;
    std::cout << "同步帧总数: " << cameraManager.getSyncFrameCount() << std::endl;
    std::cout << "丢失同步次数: " << cameraManager.getMissedSyncCount() << std::endl;
    
    // 计算同步率
    float syncRate = 0.0f;
    if (leftInfo.frameCount > 0 && rightInfo.frameCount > 0) {
        syncRate = static_cast<float>(cameraManager.getSyncFrameCount()) / 
                   std::min(leftInfo.frameCount, rightInfo.frameCount) * 100.0f;
    }
    std::cout << "同步率: " << syncRate << "%" << std::endl;
    
    // 计算平均帧率
    int totalElapsedSeconds = std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::steady_clock::now() - startTime).count();
    
    float leftFps = static_cast<float>(leftInfo.frameCount) / std::max(1, totalElapsedSeconds);
    float rightFps = static_cast<float>(rightInfo.frameCount) / std::max(1, totalElapsedSeconds);
    float syncFps = static_cast<float>(cameraManager.getSyncFrameCount()) / std::max(1, totalElapsedSeconds);
    
    std::cout << "平均帧率 - 左相机: " << leftFps << " fps, 右相机: " << rightFps 
              << " fps, 同步: " << syncFps << " fps" << std::endl;
    
    return 0;
} 