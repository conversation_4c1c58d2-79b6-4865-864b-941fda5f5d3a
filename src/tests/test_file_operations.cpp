#include <QCoreApplication>
#include <QCommandLineParser>
#include <QDebug>
#include <QDir>
#include <QFileInfo>
#include <QDateTime>

#include "infrastructure/file/file_manager.h"
#include "infrastructure/logging/logger.h"

// 不再重复定义日志宏，直接使用 logger.h 中定义的宏

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    app.setApplicationName("TestFileOperations");
    app.setApplicationVersion("1.0.0");
    
    // 命令行参数解析
    QCommandLineParser parser;
    parser.setApplicationDescription("文件操作测试程序");
    parser.addHelpOption();
    parser.addVersionOption();
    
    parser.process(app);
    
    // 初始化日志
    Logger::instance().init("logs/test_file_operations.log", LogLevel::INFO);
    Logger::instance().setConsoleEnabled(true);
    Logger::instance().setFileEnabled(true);
    LOG_INFO("文件操作测试开始");
    
    try {
        FileManager& fileManager = FileManager::instance();
        
        // 初始化文件管理器
        if (!fileManager.init("data", "temp", true)) {
            throw std::runtime_error("初始化文件管理器失败");
        }
        
        // 测试创建目录
        QString testDir = "data/test_dir";
        if (!fileManager.createDirectory(testDir)) {
            throw std::runtime_error("创建测试目录失败");
        }
        LOG_INFO(QString("创建目录成功: %1").arg(testDir));
        
        // 测试写入文件
        QString testFile = testDir + "/test.txt";
        QString content = "这是一个测试文件\n包含多行内容\n用于测试文件操作";
        if (!fileManager.writeTextFile(testFile, content)) {
            throw std::runtime_error("写入测试文件失败");
        }
        LOG_INFO(QString("写入文件成功: %1").arg(testFile));
        
        // 测试读取文件
        QString readContent;
        if (!fileManager.readTextFile(testFile, readContent)) {
            throw std::runtime_error("读取测试文件失败");
        }
        LOG_INFO(QString("读取文件成功，内容: \n%1").arg(readContent));
        
        // 测试重命名文件
        QString newTestFile = testDir + "/test_renamed.txt";
        if (!fileManager.rename(testFile, newTestFile)) {
            throw std::runtime_error("重命名文件失败");
        }
        LOG_INFO(QString("重命名文件成功: %1 -> %2").arg(testFile).arg(newTestFile));
        
        // 测试复制文件
        QString copiedFile = testDir + "/test_copied.txt";
        if (!fileManager.copyFile(newTestFile, copiedFile)) {
            throw std::runtime_error("复制文件失败");
        }
        LOG_INFO(QString("复制文件成功: %1 -> %2").arg(newTestFile).arg(copiedFile));
        
        // 测试移动文件
        QString movedFile = testDir + "/test_moved.txt";
        if (!fileManager.moveFile(copiedFile, movedFile)) {
            throw std::runtime_error("移动文件失败");
        }
        LOG_INFO(QString("移动文件成功: %1 -> %2").arg(copiedFile).arg(movedFile));
        
        // 测试搜索文件
        QStringList files = fileManager.searchFiles(testDir, "*.txt", true);
        LOG_INFO(QString("搜索到的文件: %1").arg(files.join(", ")));
        
        // 测试搜索文件内容
        QStringList matchedFiles = fileManager.searchFileContent(testDir, "测试", "*.txt", true);
        LOG_INFO(QString("包含'测试'的文件: %1").arg(matchedFiles.join(", ")));
        
        // 测试批量删除文件
        QStringList filesToDelete;
        filesToDelete << newTestFile << movedFile;
        int deletedCount = fileManager.batchDeleteFiles(filesToDelete);
        LOG_INFO(QString("成功删除 %1 个文件").arg(deletedCount));
        
        // 清理测试目录
        QDir dir(testDir);
        if (dir.exists() && !dir.removeRecursively()) {
            throw std::runtime_error("清理测试目录失败");
        }
        LOG_INFO("清理测试目录成功");
        
        LOG_INFO("文件操作测试完成");
        return 0;
        
    } catch (const std::exception& e) {
        LOG_ERROR(QString("测试过程中发生异常: %1").arg(e.what()));
        return 1;
    }
} 