void InferenceService::submitRequest(const SmartScope::InferenceRequest& request)
{
    try {
        // 检查输入图像
        if (request.left_image.empty() || request.right_image.empty()) {
            LOG_ERROR("输入图像为空");
            return;
        }

        // 记录原始尺寸
        int originalWidth = request.left_image.cols;
        int originalHeight = request.left_image.rows;
        
        LOG_INFO(QString("开始处理图像 - 尺寸: %1x%2").arg(originalWidth).arg(originalHeight));

        // 预处理图像
        cv::Mat leftProcessed = preprocessImage(request.left_image);
        cv::Mat rightProcessed = preprocessImage(request.right_image);

        // 创建输入tensor，使用原始尺寸
        std::vector<int64_t> input_shape = {1, 3, originalHeight, originalWidth};
        
        // 创建输入tensor
        auto left_tensor = createInputTensor(leftProcessed, input_shape);
        auto right_tensor = createInputTensor(rightProcessed, input_shape);

        // 准备输入数据
        std::vector<Ort::Value> input_tensors;
        input_tensors.push_back(std::move(left_tensor));
        input_tensors.push_back(std::move(right_tensor));

        // 运行推理
        LOG_INFO("开始运行推理");
        auto output_tensors = m_session->Run(Ort::RunOptions{nullptr}, 
                                           m_input_node_names.data(), 
                                           input_tensors.data(), 
                                           input_tensors.size(),
                                           m_output_node_names.data(), 
                                           m_output_node_names.size());

        // 处理输出
        if (output_tensors.empty()) {
            LOG_ERROR("推理输出为空");
            return;
        }

        // 获取视差图
        float* output_data = output_tensors[0].GetTensorData<float>();
        auto output_shape = output_tensors[0].GetTensorTypeAndShapeInfo().GetShape();
        
        // 创建视差图
        cv::Mat disparity_map(output_shape[2], output_shape[3], CV_32F);
        memcpy(disparity_map.data, output_data, output_shape[2] * output_shape[3] * sizeof(float));

        // 发送结果
        SmartScope::InferenceResult result;
        result.disparity_map = disparity_map;
        result.success = true;
        result.error_message = "";
        
        emit inferenceCompleted(result);

    } catch (const std::exception& e) {
        LOG_ERROR(QString("推理异常: %1").arg(e.what()));
        SmartScope::InferenceResult result;
        result.success = false;
        result.error_message = e.what();
        emit inferenceCompleted(result);
    }
}

cv::Mat InferenceService::preprocessImage(const cv::Mat& image)
{
    try {
        // 确保图像是RGB格式
        cv::Mat rgbImage;
        if (image.channels() == 4) {
            cv::cvtColor(image, rgbImage, cv::COLOR_BGRA2BGR);
        } else if (image.channels() == 1) {
            cv::cvtColor(image, rgbImage, cv::COLOR_GRAY2BGR);
        } else {
            rgbImage = image.clone();
        }

        // 转换为浮点型并归一化到[0,1]
        cv::Mat floatImage;
        rgbImage.convertTo(floatImage, CV_32F, 1.0/255.0);

        // 调整通道顺序从BGR到RGB
        std::vector<cv::Mat> channels;
        cv::split(floatImage, channels);
        std::swap(channels[0], channels[2]);
        cv::merge(channels, floatImage);

        return floatImage;

    } catch (const std::exception& e) {
        LOG_ERROR(QString("图像预处理异常: %1").arg(e.what()));
        throw;
    }
}

Ort::Value InferenceService::createInputTensor(const cv::Mat& image, const std::vector<int64_t>& shape)
{
    try {
        // 创建内存分配器
        auto memory_info = Ort::MemoryInfo::CreateCpu(OrtArenaAllocator, OrtMemTypeDefault);

        // 创建输入tensor
        auto tensor = Ort::Value::CreateTensor<float>(
            memory_info,
            (float*)image.data,
            image.total() * image.channels(),
            shape.data(),
            shape.size()
        );

        return tensor;
    } catch (const std::exception& e) {
        LOG_ERROR(QString("创建输入tensor异常: %1").arg(e.what()));
        throw;
    }
} 