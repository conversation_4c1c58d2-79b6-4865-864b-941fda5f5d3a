import cv2 as cv
import glob
import numpy as np
import sys
from scipy import linalg
import yaml
import os
import time

# This will contain the calibration settings from the calibration_settings.yaml file
calibration_settings = {}


# Given Projection matrices P1 and P2, and pixel coordinates point1 and point2, return triangulated 3D point.
def DLT(P1, P2, point1, point2):

    A = [
        point1[1] * P1[2, :] - P1[1, :],
        P1[0, :] - point1[0] * P1[2, :],
        point2[1] * P2[2, :] - P2[1, :],
        P2[0, :] - point2[0] * P2[2, :],
    ]
    A = np.array(A).reshape((4, 4))

    B = A.transpose() @ A
    U, s, Vh = linalg.svd(B, full_matrices=False)

    # print('Triangulated point: ')
    # print(Vh[3,0:3]/Vh[3,3])
    return Vh[3, 0:3] / Vh[3, 3]


# Open and load the calibration_settings.yaml file
def parse_calibration_settings_file(filename):

    global calibration_settings

    if not os.path.exists(filename):
        print("File does not exist:", filename)
        quit()

    print("Using for calibration settings: ", filename)

    with open(filename) as f:
        calibration_settings = yaml.safe_load(f)

    # rudimentray check to make sure correct file was loaded
    if "camera0" not in calibration_settings.keys():
        print(
            "camera0 key was not found in the settings file. Check if correct calibration_settings.yaml file was passed"
        )
        quit()


def save_frames_single_camera(camera_name):

    # create frames directory
    if not os.path.exists("frames"):
        os.mkdir("frames")

    # get settings
    camera_device_id = calibration_settings[camera_name]
    width = calibration_settings["frame_width"]
    height = calibration_settings["frame_height"]
    number_to_save = calibration_settings["mono_calibration_frames"]
    view_resize = calibration_settings["view_resize"]
    cooldown_time = calibration_settings["cooldown"]

    # open video stream and change resolution.
    cap = cv.VideoCapture(camera_device_id)
    cap.set(cv.CAP_PROP_FRAME_WIDTH, width)
    cap.set(cv.CAP_PROP_FRAME_HEIGHT, height)

    print(f"设置相机分辨率: {width}x{height}")

    saved_count = 0
    last_save_time = 0

    while True:

        ret, frame = cap.read()
        if not ret:
            # if no video data is received, can't calibrate the camera, so exit.
            print("No video data received from camera. Exiting...")
            quit()

        frame = cv.rotate(frame, cv.ROTATE_90_CLOCKWISE)
        frame_small = cv.resize(frame, None, fx=1 / view_resize, fy=1 / view_resize)

        cv.putText(
            frame_small,
            "Press 's' to save frame",
            (50, 50),
            cv.FONT_HERSHEY_COMPLEX,
            1,
            (0, 255, 0),
            1,
        )
        cv.putText(
            frame_small,
            "Num frames: " + str(saved_count),
            (50, 100),
            cv.FONT_HERSHEY_COMPLEX,
            1,
            (0, 255, 0),
            1,
        )

        # cv.imshow("frame_small", frame_small)
        cv.imshow("frame_small", resize_to_half(frame_small))
        k = cv.waitKey(1)

        if k == 27:
            # if ESC is pressed at any time, the program will exit.
            break

        if k == ord("s"):
            # Press 's' to save frame (with cooldown)
            current_time = time.time() * 1000  # 转换为毫秒
            if current_time - last_save_time >= cooldown_time:
                savename = os.path.join(
                    "frames", camera_name + "_" + str(saved_count) + ".png"
                )
                cv.imwrite(savename, frame)
                saved_count += 1
                last_save_time = current_time
                print("saved " + savename)
            else:
                remaining_time = int((cooldown_time - (current_time - last_save_time)) / 1000)
                print(f"Cooldown active, wait {remaining_time} more seconds")

        # break out of the loop when enough number of frames have been saved
        if saved_count == number_to_save:
            break

    cap.release()
    cv.destroyAllWindows()


def resize_to_half(image):
    """
    将输入图像缩放到原始大小的一半。

    :param image: 输入的OpenCV图像（Mat对象）。
    :return: 缩放后的图像。
    """
    # 获取图像的宽度和高度
    height, width = image.shape[:2]

    # 缩放图像到1/2
    resized_image = cv.resize(
        image, (width // 2, height // 2), interpolation=cv.INTER_AREA
    )

    return resized_image


# Calibrate single camera to obtain camera intrinsic parameters from saved frames.
def calibrate_camera_for_intrinsic_parameters(images_prefix):

    # NOTE: images_prefix contains camera name: "frames/camera0*".
    images_names = glob.glob(images_prefix)

    # read all frames
    images = [cv.imread(imname, 1) for imname in images_names]

    # criteria used by checkerboard pattern detector.
    # Change this if the code can't find the checkerboard.
    criteria = (cv.TERM_CRITERIA_EPS + cv.TERM_CRITERIA_MAX_ITER, 100, 0.001)

    rows = calibration_settings["checkerboard_rows"]
    columns = calibration_settings["checkerboard_columns"]
    world_scaling = calibration_settings[
        "checkerboard_box_size_scale"
    ]  # this will change to user defined length scale

    # coordinates of squares in the checkerboard world space
    objp = np.zeros((rows * columns, 3), np.float32)
    objp[:, :2] = np.mgrid[0:rows, 0:columns].T.reshape(-1, 2)
    objp = world_scaling * objp

    # frame dimensions. Frames should be the same size.
    width = images[0].shape[1]
    height = images[0].shape[0]

    # Pixel coordinates of checkerboards
    imgpoints = []  # 2d points in image plane.

    # coordinates of the checkerboard in checkerboard world space.
    objpoints = []  # 3d point in real world space

    for i, frame in enumerate(images):
        gray = cv.cvtColor(frame, cv.COLOR_BGR2GRAY)

        # find the checkerboard
        ret, corners = cv.findChessboardCorners(gray, (rows, columns), None)

        if ret == True:

            # Convolution size used to improve corner detection. Don't make this too large.
            conv_size = (11, 11)

            # opencv can attempt to improve the checkerboard coordinates
            corners = cv.cornerSubPix(gray, corners, conv_size, (-1, -1), criteria)
            cv.drawChessboardCorners(frame, (rows, columns), corners, ret)
            cv.putText(
                frame,
                'If detected points are poor, press "s" to skip this sample',
                (25, 25),
                cv.FONT_HERSHEY_COMPLEX,
                1,
                (0, 0, 255),
                1,
            )

            cv.imshow("img", resize_to_half(frame))
            k = cv.waitKey(0)

            if k & 0xFF == ord("s"):
                print("skipping")
                continue

            objpoints.append(objp)
            imgpoints.append(corners)

    cv.destroyAllWindows()
    ret, cmtx, dist, rvecs, tvecs = cv.calibrateCamera(
        objpoints, imgpoints, (width, height), None, None
    )
    print("rmse:", ret)
    print("camera matrix:\n", cmtx)
    print("distortion coeffs:", dist)

    return cmtx, dist


# save camera intrinsic parameters to file
def save_camera_intrinsics(camera_matrix, distortion_coefs, camera_name):

    # create folder if it does not exist
    if not os.path.exists("camera_parameters"):
        os.mkdir("camera_parameters")

    out_filename = os.path.join("camera_parameters", camera_name + "_intrinsics.dat")
    outf = open(out_filename, "w")

    outf.write("intrinsic:\n")
    for l in camera_matrix:
        for en in l:
            outf.write(str(en) + " ")
        outf.write("\n")

    outf.write("distortion:\n")
    for en in distortion_coefs[0]:
        outf.write(str(en) + " ")
    outf.write("\n")


def load_camera_intrinsics(camera_name):
    """
    从文件中加载相机内参和畸变系数。

    :param camera_name: 相机名称，用于构建文件名
    :return: camera_matrix, distortion_coefs
    """
    in_filename = os.path.join("camera_parameters", camera_name + "_intrinsics.dat")

    if not os.path.exists(in_filename):
        raise FileNotFoundError(f"文件 {in_filename} 不存在")

    with open(in_filename, "r") as inf:
        lines = inf.readlines()

    # 解析内参矩阵
    camera_matrix = []
    distortion_coefs = []

    reading_intrinsic = False
    reading_distortion = False

    for line in lines:
        if "intrinsic:" in line:
            reading_intrinsic = True
            reading_distortion = False
            continue
        elif "distortion:" in line:
            reading_intrinsic = False
            reading_distortion = True
            continue

        if reading_intrinsic:
            camera_matrix.append(list(map(float, line.split())))
        elif reading_distortion:
            distortion_coefs = list(map(float, line.split()))

    camera_matrix = np.array(camera_matrix)
    distortion_coefs = np.array([distortion_coefs])

    return camera_matrix, distortion_coefs


def save_frames_two_cams(camera0_name, camera1_name):

    # create frames directory
    if not os.path.exists("frames_pair"):
        os.mkdir("frames_pair")

    # settings for taking data
    view_resize = calibration_settings["view_resize"]
    number_to_save = calibration_settings["stereo_calibration_frames"]
    cooldown_time = calibration_settings["cooldown"]

    # open the video streams
    cap0 = cv.VideoCapture(calibration_settings[camera0_name])
    cap1 = cv.VideoCapture(calibration_settings[camera1_name])

    # 从配置文件读取分辨率
    width = calibration_settings["frame_width"]
    height = calibration_settings["frame_height"]

    cap0.set(cv.CAP_PROP_FRAME_WIDTH, width)
    cap0.set(cv.CAP_PROP_FRAME_HEIGHT, height)
    cap1.set(cv.CAP_PROP_FRAME_WIDTH, width)
    cap1.set(cv.CAP_PROP_FRAME_HEIGHT, height)

    print(f"设置双相机分辨率: {width}x{height}")

    saved_count = 0
    while True:
        ret0, frame0 = cap0.read()
        ret1, frame1 = cap1.read()

        if not ret0 or not ret1:
            print("Cameras not returning video data. Exiting...")
            quit()

        frame0 = cv.rotate(frame0, cv.ROTATE_90_CLOCKWISE)
        frame1 = cv.rotate(frame1, cv.ROTATE_90_CLOCKWISE)

        frame0_small = cv.resize(
            frame0, None, fx=1.0 / view_resize, fy=1.0 / view_resize
        )
        frame1_small = cv.resize(
            frame1, None, fx=1.0 / view_resize, fy=1.0 / view_resize
        )

        cv.putText(
            frame0_small,
            "Press 's' to save frame",
            (50, 50),
            cv.FONT_HERSHEY_COMPLEX,
            1,
            (0, 255, 0),
            1,
        )
        cv.putText(
            frame0_small,
            "Num frames: " + str(saved_count),
            (50, 100),
            cv.FONT_HERSHEY_COMPLEX,
            1,
            (0, 255, 0),
            1,
        )
        cv.putText(
            frame1_small,
            "Press 's' to save frame",
            (50, 50),
            cv.FONT_HERSHEY_COMPLEX,
            1,
            (0, 255, 0),
            1,
        )
        cv.putText(
            frame1_small,
            "Num frames: " + str(saved_count),
            (50, 100),
            cv.FONT_HERSHEY_COMPLEX,
            1,
            (0, 255, 0),
            1,
        )

        cv.imshow("frame0_small", resize_to_half(frame0_small))
        cv.imshow("frame1_small", resize_to_half(frame1_small))
        k = cv.waitKey(1)

        if k == 27:
            # if ESC is pressed at any time, the program will exit.
            quit()

        if k == ord("s"):
            # Press 's' to save frames
            savename0 = os.path.join(
                "frames_pair", camera0_name + "_" + str(saved_count) + ".png"
            )
            cv.imwrite(savename0, frame0)

            savename1 = os.path.join(
                "frames_pair", camera1_name + "_" + str(saved_count) + ".png"
            )
            cv.imwrite(savename1, frame1)

            saved_count += 1

        # break out of the loop when enough number of frames have been saved
        if saved_count == number_to_save:
            break

    cv.destroyAllWindows()


def stereo_calibrate(mtx0, dist0, mtx1, dist1, frames_prefix_c0, frames_prefix_c1):
    # 读取同步的帧
    c0_images_names = sorted(glob.glob(frames_prefix_c0))
    c1_images_names = sorted(glob.glob(frames_prefix_c1))

    # 打开图像
    c0_images = [cv.imread(imname, 1) for imname in c0_images_names]
    c1_images = [cv.imread(imname, 1) for imname in c1_images_names]

    # 终止标准
    criteria = (cv.TERM_CRITERIA_EPS + cv.TERM_CRITERIA_MAX_ITER, 100, 1e-5)

    rows = calibration_settings["checkerboard_rows"]
    columns = calibration_settings["checkerboard_columns"]
    world_scaling = calibration_settings["checkerboard_box_size_scale"]

    # 世界坐标中的棋盘格点
    objp = np.zeros((rows * columns, 3), np.float32)
    objp[:, :2] = np.mgrid[0:rows, 0:columns].T.reshape(-1, 2)
    objp *= world_scaling

    # 图像点和对象点
    imgpoints_left = []
    imgpoints_right = []
    objpoints = []

    for frame0, frame1 in zip(c0_images, c1_images):
        gray1 = cv.cvtColor(frame0, cv.COLOR_BGR2GRAY)
        gray2 = cv.cvtColor(frame1, cv.COLOR_BGR2GRAY)
        ret1, corners1 = cv.findChessboardCorners(gray1, (rows, columns), None)
        ret2, corners2 = cv.findChessboardCorners(gray2, (rows, columns), None)

        if ret1 and ret2:
            corners1 = cv.cornerSubPix(gray1, corners1, (11, 11), (-1, -1), criteria)
            corners2 = cv.cornerSubPix(gray2, corners2, (11, 11), (-1, -1), criteria)

            objpoints.append(objp)
            imgpoints_left.append(corners1)
            imgpoints_right.append(corners2)

    # 改进的立体标定标志：允许优化内参以获得更好的精度
    stereocalibration_flags = (cv.CALIB_RATIONAL_MODEL +
                              cv.CALIB_ZERO_TANGENT_DIST +
                              cv.CALIB_SAME_FOCAL_LENGTH)
    ret, CM1, dist0, CM2, dist1, R, T, E, F = cv.stereoCalibrate(
        objpoints,
        imgpoints_left,
        imgpoints_right,
        mtx0,
        dist0,
        mtx1,
        dist1,
        gray1.shape[::-1],
        criteria=criteria,
        flags=stereocalibration_flags,
    )

    print("标定误差（RMSE）: ", ret)

    # 添加相机参数分析
    print("\n=== 相机参数分析 ===")
    print(f"左相机焦距: fx={CM1[0,0]:.2f}, fy={CM1[1,1]:.2f}")
    print(f"左相机主点: cx={CM1[0,2]:.2f}, cy={CM1[1,2]:.2f}")
    print(f"右相机焦距: fx={CM2[0,0]:.2f}, fy={CM2[1,1]:.2f}")
    print(f"右相机主点: cx={CM2[0,2]:.2f}, cy={CM2[1,2]:.2f}")

    # 计算主点偏差
    cx_diff = abs(CM1[0,2] - CM2[0,2])
    cy_diff = abs(CM1[1,2] - CM2[1,2])
    print(f"主点差异: Δcx={cx_diff:.2f}, Δcy={cy_diff:.2f}")

    if cx_diff > 50 or cy_diff > 50:
        print("警告：主点差异过大，可能影响立体校正效果！")
        print("建议：")
        print("1. 检查相机安装是否稳定")
        print("2. 重新采集标定图像，确保左右相机同时拍摄")
        print("3. 检查标定板在两个相机视野中的位置是否一致")

    # 计算基线长度
    baseline = np.linalg.norm(T)
    print(f"基线长度: {baseline:.2f} mm")
    print("===================")

    # 计算和打印重投影误差
    def calculate_reprojection_error(
        objpoints,
        imgpoints_left,
        imgpoints_right,
        rvecs,
        tvecs,
        cmtx0,
        dist0,
        cmtx1,
        dist1,
    ):
        total_error = 0
        for i in range(len(objpoints)):
            imgpoints_left_proj, _ = cv.projectPoints(
                objpoints[i], rvecs[i][0], tvecs[i][0], cmtx0, dist0
            )
            imgpoints_right_proj, _ = cv.projectPoints(
                objpoints[i], rvecs[i][1], tvecs[i][1], cmtx1, dist1
            )

            error_left = cv.norm(
                imgpoints_left[i], imgpoints_left_proj, cv.NORM_L2
            ) / len(imgpoints_left_proj)
            error_right = cv.norm(
                imgpoints_right[i], imgpoints_right_proj, cv.NORM_L2
            ) / len(imgpoints_right_proj)

            total_error += error_left + error_right

        mean_error = total_error / (2 * len(objpoints))
        print(f"重投影误差: {mean_error}")
        return mean_error

    # 计算旋转和平移向量
    rvecs_left, tvecs_left = [], []
    rvecs_right, tvecs_right = [], []

    for i in range(len(objpoints)):
        _, rvec_l, tvec_l = cv.solvePnP(objpoints[i], imgpoints_left[i], mtx0, dist0)
        _, rvec_r, tvec_r = cv.solvePnP(objpoints[i], imgpoints_right[i], mtx1, dist1)

        rvecs_left.append(rvec_l)
        tvecs_left.append(tvec_l)
        rvecs_right.append(rvec_r)
        tvecs_right.append(tvec_r)

    calculate_reprojection_error(
        objpoints,
        imgpoints_left,
        imgpoints_right,
        list(zip(rvecs_left, rvecs_right)),
        list(zip(tvecs_left, tvecs_right)),
        mtx0,
        dist0,
        mtx1,
        dist1,
    )

    # 计算立体校正参数
    print("计算立体校正参数...")
    image_size = gray1.shape[::-1]  # (width, height)

    # 尝试不同的alpha值找到最佳ROI匹配
    best_alpha = -1
    best_roi_diff = float('inf')
    best_result = None

    for alpha in [0, 0.5, 1, -1]:
        R1_test, R2_test, P1_test, P2_test, Q_test, roi1_test, roi2_test = cv.stereoRectify(
            CM1, dist0, CM2, dist1, image_size, R, T, alpha=alpha
        )

        # 计算ROI差异
        roi_diff = abs(roi1_test[2] - roi2_test[2]) + abs(roi1_test[3] - roi2_test[3])

        print(f"Alpha={alpha}: 左ROI={roi1_test}, 右ROI={roi2_test}, 差异={roi_diff}")

        if roi_diff < best_roi_diff:
            best_roi_diff = roi_diff
            best_alpha = alpha
            best_result = (R1_test, R2_test, P1_test, P2_test, Q_test, roi1_test, roi2_test)

    print(f"选择最佳alpha={best_alpha}, ROI差异={best_roi_diff}")
    R1, R2, P1, P2, Q, validPixROI1, validPixROI2 = best_result

    print("立体校正参数计算完成")
    print("左相机有效ROI:", validPixROI1)
    print("右相机有效ROI:", validPixROI2)

    # 检查ROI一致性
    if validPixROI1[2] != validPixROI2[2] or validPixROI1[3] != validPixROI2[3]:
        print("警告：左右相机ROI尺寸不一致！")
        print(f"左相机ROI尺寸: {validPixROI1[2]}x{validPixROI1[3]}")
        print(f"右相机ROI尺寸: {validPixROI2[2]}x{validPixROI2[3]}")

        # 计算重叠区域
        left_x1, left_y1 = validPixROI1[0], validPixROI1[1]
        left_x2, left_y2 = left_x1 + validPixROI1[2], left_y1 + validPixROI1[3]

        right_x1, right_y1 = validPixROI2[0], validPixROI2[1]
        right_x2, right_y2 = right_x1 + validPixROI2[2], right_y1 + validPixROI2[3]

        # 找到重叠区域
        overlap_x1 = max(left_x1, right_x1)
        overlap_y1 = max(left_y1, right_y1)
        overlap_x2 = min(left_x2, right_x2)
        overlap_y2 = min(left_y2, right_y2)

        if overlap_x2 > overlap_x1 and overlap_y2 > overlap_y1:
            # 使用重叠区域作为统一ROI
            unified_width = overlap_x2 - overlap_x1
            unified_height = overlap_y2 - overlap_y1

            validPixROI1 = (overlap_x1, overlap_y1, unified_width, unified_height)
            validPixROI2 = (overlap_x1, overlap_y1, unified_width, unified_height)

            print(f"使用重叠区域作为统一ROI: ({overlap_x1}, {overlap_y1}, {unified_width}, {unified_height})")
        else:
            # 如果没有重叠，使用较小的尺寸
            min_width = min(validPixROI1[2], validPixROI2[2])
            min_height = min(validPixROI1[3], validPixROI2[3])

            validPixROI1 = (0, 0, min_width, min_height)
            validPixROI2 = (0, 0, min_width, min_height)

            print(f"无重叠区域，使用最小尺寸: {min_width}x{min_height}")

        print("最终统一ROI:")
        print("左相机ROI:", validPixROI1)
        print("右相机ROI:", validPixROI2)

    cv.destroyAllWindows()
    return R, T


# Converts Rotation matrix R and Translation vector T into a homogeneous representation matrix
def _make_homogeneous_rep_matrix(R, t):
    P = np.zeros((4, 4))
    P[:3, :3] = R
    P[:3, 3] = t.reshape(3)
    P[3, 3] = 1

    return P


# Turn camera calibration data into projection matrix
def get_projection_matrix(cmtx, R, T):
    P = cmtx @ _make_homogeneous_rep_matrix(R, T)[:3, :]
    return P


# After calibrating, we can see shifted coordinate axes in the video feeds directly
def check_calibration(
    camera0_name, camera0_data, camera1_name, camera1_data, _zshift=50.0
):

    cmtx0 = np.array(camera0_data[0])
    dist0 = np.array(camera0_data[1])
    R0 = np.array(camera0_data[2])
    T0 = np.array(camera0_data[3])
    cmtx1 = np.array(camera1_data[0])
    dist1 = np.array(camera1_data[1])
    R1 = np.array(camera1_data[2])
    T1 = np.array(camera1_data[3])

    P0 = get_projection_matrix(cmtx0, R0, T0)
    P1 = get_projection_matrix(cmtx1, R1, T1)

    # define coordinate axes in 3D space. These are just the usual coorindate vectors
    coordinate_points = np.array(
        [[0.0, 0.0, 0.0], [1.0, 0.0, 0.0], [0.0, 1.0, 0.0], [0.0, 0.0, 1.0]]
    )
    z_shift = np.array([0.0, 0.0, _zshift]).reshape((1, 3))
    # increase the size of the coorindate axes and shift in the z direction
    draw_axes_points = 5 * coordinate_points + z_shift

    # project 3D points to each camera view manually. This can also be done using cv.projectPoints()
    # Note that this uses homogenous coordinate formulation
    pixel_points_camera0 = []
    pixel_points_camera1 = []
    for _p in draw_axes_points:
        X = np.array([_p[0], _p[1], _p[2], 1.0])

        # project to camera0
        uv = P0 @ X
        uv = np.array([uv[0], uv[1]]) / uv[2]
        pixel_points_camera0.append(uv)

        # project to camera1
        uv = P1 @ X
        uv = np.array([uv[0], uv[1]]) / uv[2]
        pixel_points_camera1.append(uv)

    # these contain the pixel coorindates in each camera view as: (pxl_x, pxl_y)
    pixel_points_camera0 = np.array(pixel_points_camera0)
    pixel_points_camera1 = np.array(pixel_points_camera1)

    # open the video streams
    cap0 = cv.VideoCapture(calibration_settings[camera0_name])
    cap1 = cv.VideoCapture(calibration_settings[camera1_name])

    # 从配置文件读取分辨率
    width = calibration_settings['frame_width']
    height = calibration_settings['frame_height']

    cap0.set(cv.CAP_PROP_FRAME_WIDTH, width)
    cap0.set(cv.CAP_PROP_FRAME_HEIGHT, height)
    cap1.set(cv.CAP_PROP_FRAME_WIDTH, width)
    cap1.set(cv.CAP_PROP_FRAME_HEIGHT, height)

    print(f"检查标定 - 设置双相机分辨率: {width}x{height}")

    while True:

        ret0, frame0 = cap0.read()
        ret1, frame1 = cap1.read()

        if not ret0 or not ret1:
            print("Video stream not returning frame data")
            quit()

        frame0 = cv.rotate(frame0, cv.ROTATE_90_CLOCKWISE)
        frame1 = cv.rotate(frame1, cv.ROTATE_90_CLOCKWISE)

        # follow RGB colors to indicate XYZ axes respectively
        colors = [(0, 0, 255), (0, 255, 0), (255, 0, 0)]
        # draw projections to camera0
        origin = tuple(pixel_points_camera0[0].astype(np.int32))
        for col, _p in zip(colors, pixel_points_camera0[1:]):
            _p = tuple(_p.astype(np.int32))
            cv.line(frame0, origin, _p, col, 2)

        # draw projections to camera1
        origin = tuple(pixel_points_camera1[0].astype(np.int32))
        for col, _p in zip(colors, pixel_points_camera1[1:]):
            _p = tuple(_p.astype(np.int32))
            cv.line(frame1, origin, _p, col, 2)

        cv.imshow("frame0", resize_to_half(frame0))
        cv.imshow("frame1", resize_to_half(frame1))

        k = cv.waitKey(1)
        if k == 27:
            break

    cv.destroyAllWindows()


def get_world_space_origin(cmtx, dist, img_path):

    frame = cv.imread(img_path, 1)

    # calibration pattern settings
    rows = calibration_settings["checkerboard_rows"]
    columns = calibration_settings["checkerboard_columns"]
    world_scaling = calibration_settings["checkerboard_box_size_scale"]

    # coordinates of squares in the checkerboard world space
    objp = np.zeros((rows * columns, 3), np.float32)
    objp[:, :2] = np.mgrid[0:rows, 0:columns].T.reshape(-1, 2)
    objp = world_scaling * objp

    gray = cv.cvtColor(frame, cv.COLOR_BGR2GRAY)
    ret, corners = cv.findChessboardCorners(gray, (rows, columns), None)

    cv.drawChessboardCorners(frame, (rows, columns), corners, ret)
    cv.putText(
        frame,
        "If you don't see detected points, try with a different image",
        (50, 50),
        cv.FONT_HERSHEY_COMPLEX,
        1,
        (0, 0, 255),
        1,
    )
    cv.imshow("img", resize_to_half(frame))
    cv.waitKey(0)

    ret, rvec, tvec = cv.solvePnP(objp, corners, cmtx, dist)
    R, _ = cv.Rodrigues(rvec)  # rvec is Rotation matrix in Rodrigues vector form

    return R, tvec


def get_cam1_to_world_transforms(
    cmtx0, dist0, R_W0, T_W0, cmtx1, dist1, R_01, T_01, image_path0, image_path1
):

    frame0 = cv.imread(image_path0, 1)
    frame1 = cv.imread(image_path1, 1)

    unitv_points = 5 * np.array(
        [[0, 0, 0], [1, 0, 0], [0, 1, 0], [0, 0, 1]], dtype="float32"
    ).reshape((4, 1, 3))
    # axes colors are RGB format to indicate XYZ axes.
    colors = [(0, 0, 255), (0, 255, 0), (255, 0, 0)]

    # project origin points to frame 0
    points, _ = cv.projectPoints(unitv_points, R_W0, T_W0, cmtx0, dist0)
    points = points.reshape((4, 2)).astype(np.int32)
    origin = tuple(points[0])
    for col, _p in zip(colors, points[1:]):
        _p = tuple(_p.astype(np.int32))
        cv.line(frame0, origin, _p, col, 2)

    # project origin points to frame1
    R_W1 = R_01 @ R_W0
    T_W1 = R_01 @ T_W0 + T_01
    points, _ = cv.projectPoints(unitv_points, R_W1, T_W1, cmtx1, dist1)
    points = points.reshape((4, 2)).astype(np.int32)
    origin = tuple(points[0])
    for col, _p in zip(colors, points[1:]):
        _p = tuple(_p.astype(np.int32))
        cv.line(frame1, origin, _p, col, 2)

    cv.imshow("frame0", frame0)
    cv.imshow("frame1", frame1)
    # cv.imshow("frame0", resize_to_half(frame0))
    # cv.imshow("frame1", resize_to_half(frame1))
    cv.waitKey(0)

    return R_W1, T_W1


def save_extrinsic_calibration_parameters(R0, T0, R1, T1, prefix=""):

    # create folder if it does not exist
    if not os.path.exists("camera_parameters"):
        os.mkdir("camera_parameters")

    camera0_rot_trans_filename = os.path.join(
        "camera_parameters", prefix + "camera0_rot_trans.dat"
    )
    outf = open(camera0_rot_trans_filename, "w")

    outf.write("R:\n")
    for l in R0:
        for en in l:
            outf.write(str(en) + " ")
        outf.write("\n")

    outf.write("T:\n")
    for l in T0:
        for en in l:
            outf.write(str(en) + " ")
        outf.write("\n")
    outf.close()

    # R1 and T1 are just stereo calibration returned values
    camera1_rot_trans_filename = os.path.join(
        "camera_parameters", prefix + "camera1_rot_trans.dat"
    )
    outf = open(camera1_rot_trans_filename, "w")

    outf.write("R:\n")
    for l in R1:
        for en in l:
            outf.write(str(en) + " ")
        outf.write("\n")

    outf.write("T:\n")
    for l in T1:
        for en in l:
            outf.write(str(en) + " ")
        outf.write("\n")
    outf.close()

    return R0, T0, R1, T1




if __name__ == "__main__":

    if len(sys.argv) != 2:
        print(
            'Call with settings filename: "python3 calibrate.py calibration_settings.yaml"'
        )
        quit()

    # Open and parse the settings file
    parse_calibration_settings_file(sys.argv[1])

    """Step1. Save calibration frames for single cameras"""
    save_frames_single_camera("camera0")  # save frames for camera0
    save_frames_single_camera("camera1")  # save frames for camera1

    """Step2. Obtain camera intrinsic matrices and save them"""
    # camera0 intrinsics
    images_prefix = os.path.join("frames", "camera0*")
    cmtx0, dist0 = calibrate_camera_for_intrinsic_parameters(images_prefix)
    save_camera_intrinsics(
        cmtx0, dist0, "camera0"
    )  # this will write cmtx and dist to disk
    # camera1 intrinsics
    images_prefix = os.path.join("frames", "camera1*")
    cmtx1, dist1 = calibrate_camera_for_intrinsic_parameters(images_prefix)
    save_camera_intrinsics(
        cmtx1, dist1, "camera1"
    )  # this will write cmtx and dist to disk

    # cmtx0, dist0 = load_camera_intrinsics("camera0")
    # cmtx1, dist1 = load_camera_intrinsics("camera1")

    """Step3. Save calibration frames for both cameras simultaneously"""
    save_frames_two_cams("camera0", "camera1")  # save simultaneous frames

    """Step4. Use paired calibration pattern frames to obtain camera0 to camera1 rotation and translation"""
    frames_prefix_c0 = os.path.join("frames_pair", "camera0*")
    frames_prefix_c1 = os.path.join("frames_pair", "camera1*")
    R, T = stereo_calibrate(
        cmtx0, dist0, cmtx1, dist1, frames_prefix_c0, frames_prefix_c1
    )

    """Step5. Save calibration data where camera0 defines the world space origin."""
    # camera0 rotation and translation is identity matrix and zeros vector
    R0 = np.eye(3, dtype=np.float32)
    T0 = np.array([0.0, 0.0, 0.0]).reshape((3, 1))

    save_extrinsic_calibration_parameters(
        R0, T0, R, T
    )  # this will write R and T to disk
    R1 = R
    T1 = T  # to avoid confusion, camera1 R and T are labeled R1 and T1
    # check your calibration makes sense
    camera0_data = [cmtx0, dist0, R0, T0]
    camera1_data = [cmtx1, dist1, R1, T1]
    # check_calibration('camera0', camera0_data, 'camera1', camera1_data, _zshift = 60.)

    """Optional. Define a different origin point and save the calibration data"""
    # #get the world to camera0 rotation and translation
    # R_W0, T_W0 = get_world_space_origin(cmtx0, dist0, os.path.join('frames_pair', 'camera0_4.png'))
    # #get rotation and translation from world directly to camera1
    # R_W1, T_W1 = get_cam1_to_world_transforms(cmtx0, dist0, R_W0, T_W0,
    #                                           cmtx1, dist1, R1, T1,
    #                                           os.path.join('frames_pair', 'camera0_4.png'),
    #                                           os.path.join('frames_pair', 'camera1_4.png'),)

    # #save rotation and translation parameters to disk
    # save_extrinsic_calibration_parameters(R_W0, T_W0, R_W1, T_W1, prefix = 'world_to_') #this will write R and T to disk
