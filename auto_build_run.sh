#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[信息] $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}[警告] $1${NC}"
}

log_error() {
    echo -e "${RED}[错误] $1${NC}"
}

log_debug() {
    echo -e "${BLUE}[调试] $1${NC}"
}

# 保存项目根目录路径
PROJECT_ROOT="/home/<USER>/App/Qt/SmartScope"

# 确保在项目根目录下
cd $PROJECT_ROOT

# 显示当前目录
log_info "当前工作目录: $(pwd)"

# 设置库路径
export LD_LIBRARY_PATH=$PWD:$LD_LIBRARY_PATH
log_info "设置库路径: $LD_LIBRARY_PATH"

# 直接在项目根目录进行配置和构建
log_info "在项目根目录执行CMake配置..."

# 如果CMakeCache.txt不存在，则需要首次配置
if [ ! -f "CMakeCache.txt" ]; then
    log_info "首次配置CMake..."
    cmake -DCMAKE_BUILD_TYPE=Debug .
else
    log_info "使用现有CMake配置..."
fi

if [ $? -eq 0 ]; then
    # 确认当前目录
    log_info "当前目录: $(pwd)"
    
    # 在 make 之前检查 Makefile 是否存在
    if [ ! -f "Makefile" ]; then
        log_warning "Makefile 不存在，重新运行 CMake..."
        cmake -DCMAKE_BUILD_TYPE=Debug .
        if [ $? -ne 0 ]; then
            log_error "重新运行 CMake 失败！"
            exit 1
        fi
    fi

    # 编译程序
    log_info "增量编译主程序..."
    make -j4
    
    if [ $? -eq 0 ]; then
        # 检查可执行文件是否存在
        if [ -f "SmartScopeQt" ]; then
            log_info "编译成功！运行程序..."
            # 运行程序
            ./SmartScopeQt
        else
            log_error "可执行文件未生成，但编译过程未报错。检查链接问题..."
            
            # 检查链接错误
            log_warning "检查链接错误..."
            make VERBOSE=1 | grep -i "undefined reference"
            
            # 查找可能的可执行文件
            log_info "搜索可能的可执行文件位置..."
            find $PROJECT_ROOT -name "SmartScopeQt" -type f -executable 2>/dev/null
            
            # 提供解决方案建议
            log_info "可能的解决方案:"
            log_info "1. 检查 CMakeLists.txt 中的库依赖关系"
            log_info "2. 确保所有类的 Q_OBJECT 宏正确声明"
            log_info "3. 检查 MOC 文件是否正确生成"
            log_info "4. 确认 CMakeLists.txt 中设置的可执行文件输出路径"
            log_info "5. 尝试手动运行 qmake 生成 Makefile"
        fi
    else
        log_error "编译失败！"
        
        # # 显示最后10个错误
        # log_warning "显示最后10个错误:"
        # make -j4 2>&1 | grep -i "error:" | tail -10
        
        exit 1
    fi
else
    log_error "CMake配置失败！"
    exit 1
fi 