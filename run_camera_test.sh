#!/bin/bash

echo "=== 相机测试脚本 ==="
echo

# 检查当前目录
pwd

# 检查相机设备
echo "检查相机设备..."
ls -la /dev/video*
echo

# 检查OpenCV是否安装
echo "检查OpenCV..."
pkg-config --modversion opencv4 2>/dev/null || pkg-config --modversion opencv 2>/dev/null || echo "OpenCV未找到"
echo

# 选择测试方式
echo "选择测试方式:"
echo "1. Python版本 (需要python3和opencv-python)"
echo "2. C++版本 (需要编译)"
echo "3. 简单的v4l2测试"
echo "4. 查看相机详细信息"
read -p "请选择 (1-4): " choice

case $choice in
    1)
        echo "运行Python版本..."
        if command -v python3 &> /dev/null; then
            python3 camera_test.py
        else
            echo "错误: python3未安装"
        fi
        ;;
    2)
        echo "编译并运行C++版本..."
        if make -f Makefile_camera_test; then
            ./camera_test
        else
            echo "编译失败"
        fi
        ;;
    3)
        echo "运行v4l2测试..."
        echo "可用的视频设备:"
        for dev in /dev/video*; do
            if [ -c "$dev" ]; then
                echo "设备: $dev"
                v4l2-ctl --device=$dev --list-formats-ext 2>/dev/null || echo "  无法获取格式信息"
            fi
        done
        ;;
    4)
        echo "查看相机详细信息..."
        for dev in /dev/video*; do
            if [ -c "$dev" ]; then
                echo "=== $dev ==="
                v4l2-ctl --device=$dev --all 2>/dev/null || echo "无法获取设备信息"
                echo
            fi
        done
        ;;
    *)
        echo "无效选择"
        ;;
esac
