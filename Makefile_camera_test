# Makefile for camera test
CXX = g++
CXXFLAGS = -std=c++11 -Wall -O2
OPENCV_FLAGS = `pkg-config --cflags --libs opencv4`

# 如果opencv4不可用，尝试opencv
ifeq ($(shell pkg-config --exists opencv4; echo $$?), 1)
    OPENCV_FLAGS = `pkg-config --cflags --libs opencv`
endif

TARGET = camera_test
SOURCE = camera_test.cpp

all: $(TARGET)

$(TARGET): $(SOURCE)
	$(CXX) $(CXXFLAGS) -o $(TARGET) $(SOURCE) $(OPENCV_FLAGS)

clean:
	rm -f $(TARGET)

test: $(TARGET)
	./$(TARGET)

.PHONY: all clean test
