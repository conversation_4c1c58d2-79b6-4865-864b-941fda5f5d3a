#include <opencv2/opencv.hpp>
#include <iostream>
#include <vector>
#include <chrono>
#include <thread>

using namespace cv;
using namespace std;

// 列出所有可用的相机设备
vector<int> listCameras() {
    cout << "正在扫描可用的相机设备..." << endl;
    vector<int> availableCameras;
    
    // 测试前10个设备索引
    for (int i = 0; i < 10; i++) {
        VideoCapture cap(i);
        if (cap.isOpened()) {
            Mat frame;
            bool ret = cap.read(frame);
            if (ret && !frame.empty()) {
                cout << "相机 " << i << ": 分辨率 " << frame.cols << "x" << frame.rows << endl;
                availableCameras.push_back(i);
            }
            cap.release();
        }
    }
    
    return availableCameras;
}

// 测试单个相机
VideoCapture* testSingleCamera(int cameraId, const string& windowName, Point position) {
    cout << "正在测试相机 " << cameraId << "..." << endl;
    
    VideoCapture* cap = new VideoCapture(cameraId);
    
    if (!cap->isOpened()) {
        cout << "无法打开相机 " << cameraId << endl;
        delete cap;
        return nullptr;
    }
    
    // 设置相机参数
    cap->set(CAP_PROP_FRAME_WIDTH, 1920);
    cap->set(CAP_PROP_FRAME_HEIGHT, 1080);
    cap->set(CAP_PROP_FPS, 30);
    
    // 获取实际参数
    int width = (int)cap->get(CAP_PROP_FRAME_WIDTH);
    int height = (int)cap->get(CAP_PROP_FRAME_HEIGHT);
    double fps = cap->get(CAP_PROP_FPS);
    
    cout << "相机 " << cameraId << " 参数: " << width << "x" << height << " @ " << fps << "fps" << endl;
    
    // 创建窗口
    namedWindow(windowName, WINDOW_NORMAL);
    resizeWindow(windowName, 640, 480);
    moveWindow(windowName, position.x, position.y);
    
    return cap;
}

int main() {
    cout << "=== 相机测试程序 ===" << endl;
    cout << "按 'q' 退出程序" << endl;
    cout << "按 's' 保存当前帧" << endl;
    cout << "按 'r' 重新初始化相机" << endl;
    cout << endl;
    
    // 列出可用相机
    vector<int> availableCameras = listCameras();
    cout << "发现 " << availableCameras.size() << " 个可用相机" << endl;
    
    if (availableCameras.empty()) {
        cout << "错误: 没有发现可用的相机设备" << endl;
        return -1;
    }
    
    // 选择要测试的相机
    int cameraLeft = availableCameras[0];
    int cameraRight = -1;
    
    if (availableCameras.size() >= 2) {
        cameraRight = availableCameras[1];
        cout << "将测试相机 " << cameraLeft << " (左) 和相机 " << cameraRight << " (右)" << endl;
    } else {
        cout << "只发现一个相机，将测试相机 " << cameraLeft << endl;
    }
    
    // 初始化相机
    VideoCapture* capLeft = testSingleCamera(cameraLeft, "左相机", Point(0, 0));
    VideoCapture* capRight = nullptr;
    
    if (cameraRight != -1) {
        capRight = testSingleCamera(cameraRight, "右相机", Point(660, 0));
    }
    
    if (capLeft == nullptr) {
        cout << "错误: 无法初始化任何相机" << endl;
        return -1;
    }
    
    // 统计信息
    int frameCount = 0;
    auto startTime = chrono::steady_clock::now();
    auto lastFpsTime = startTime;
    
    cout << "\n开始读取相机画面..." << endl;
    
    try {
        while (true) {
            auto currentTime = chrono::steady_clock::now();
            
            // 读取左相机
            Mat frameLeft;
            bool retLeft = capLeft->read(frameLeft);
            if (!retLeft || frameLeft.empty()) {
                cout << "警告: 左相机读取失败" << endl;
                frameLeft = Mat::zeros(480, 640, CV_8UC3);
                putText(frameLeft, "Left Camera Error", Point(50, 240), 
                       FONT_HERSHEY_SIMPLEX, 1, Scalar(0, 0, 255), 2);
            }
            
            // 读取右相机
            Mat frameRight;
            if (capRight != nullptr) {
                bool retRight = capRight->read(frameRight);
                if (!retRight || frameRight.empty()) {
                    cout << "警告: 右相机读取失败" << endl;
                    frameRight = Mat::zeros(480, 640, CV_8UC3);
                    putText(frameRight, "Right Camera Error", Point(50, 240), 
                           FONT_HERSHEY_SIMPLEX, 1, Scalar(0, 0, 255), 2);
                }
            }
            
            // 添加信息到画面
            frameCount++;
            
            // 计算FPS
            auto elapsed = chrono::duration_cast<chrono::seconds>(currentTime - lastFpsTime);
            if (elapsed.count() >= 1) {
                auto totalElapsed = chrono::duration_cast<chrono::milliseconds>(currentTime - startTime);
                double fps = frameCount * 1000.0 / totalElapsed.count();
                lastFpsTime = currentTime;
                cout << "FPS: " << fps << ", 总帧数: " << frameCount << endl;
            }
            
            // 在画面上显示信息
            auto totalElapsed = chrono::duration_cast<chrono::milliseconds>(currentTime - startTime);
            string infoText = "Frame: " + to_string(frameCount) + ", Time: " + to_string(totalElapsed.count() / 1000.0) + "s";
            
            putText(frameLeft, infoText, Point(10, 30), 
                   FONT_HERSHEY_SIMPLEX, 0.7, Scalar(0, 255, 0), 2);
            putText(frameLeft, "Camera " + to_string(cameraLeft), Point(10, 60), 
                   FONT_HERSHEY_SIMPLEX, 0.7, Scalar(255, 255, 0), 2);
            
            if (!frameRight.empty()) {
                putText(frameRight, infoText, Point(10, 30), 
                       FONT_HERSHEY_SIMPLEX, 0.7, Scalar(0, 255, 0), 2);
                putText(frameRight, "Camera " + to_string(cameraRight), Point(10, 60), 
                       FONT_HERSHEY_SIMPLEX, 0.7, Scalar(255, 255, 0), 2);
            }
            
            // 显示画面
            imshow("左相机", frameLeft);
            if (!frameRight.empty()) {
                imshow("右相机", frameRight);
            }
            
            // 处理按键
            char key = waitKey(1) & 0xFF;
            if (key == 'q') {
                cout << "用户退出" << endl;
                break;
            } else if (key == 's') {
                // 保存当前帧
                auto now = chrono::system_clock::now();
                auto time_t = chrono::system_clock::to_time_t(now);
                auto tm = *localtime(&time_t);
                
                char timestamp[20];
                strftime(timestamp, sizeof(timestamp), "%Y%m%d_%H%M%S", &tm);
                
                string leftFilename = "left_camera_" + string(timestamp) + ".jpg";
                string rightFilename = "right_camera_" + string(timestamp) + ".jpg";
                
                imwrite(leftFilename, frameLeft);
                if (!frameRight.empty()) {
                    imwrite(rightFilename, frameRight);
                }
                cout << "已保存图片: " << timestamp << endl;
            } else if (key == 'r') {
                // 重新初始化相机
                cout << "重新初始化相机..." << endl;
                if (capLeft) {
                    capLeft->release();
                    delete capLeft;
                }
                if (capRight) {
                    capRight->release();
                    delete capRight;
                }
                
                this_thread::sleep_for(chrono::seconds(1));
                
                capLeft = testSingleCamera(cameraLeft, "左相机", Point(0, 0));
                if (cameraRight != -1) {
                    capRight = testSingleCamera(cameraRight, "右相机", Point(660, 0));
                }
                
                if (capLeft == nullptr) {
                    cout << "重新初始化失败" << endl;
                    break;
                }
            }
        }
    } catch (const exception& e) {
        cout << "程序异常: " << e.what() << endl;
    }
    
    // 清理资源
    if (capLeft) {
        capLeft->release();
        delete capLeft;
    }
    if (capRight) {
        capRight->release();
        delete capRight;
    }
    destroyAllWindows();
    
    // 显示统计信息
    auto endTime = chrono::steady_clock::now();
    auto totalTime = chrono::duration_cast<chrono::milliseconds>(endTime - startTime);
    double avgFps = frameCount * 1000.0 / totalTime.count();
    
    cout << "\n=== 测试完成 ===" << endl;
    cout << "总运行时间: " << totalTime.count() / 1000.0 << "秒" << endl;
    cout << "总帧数: " << frameCount << endl;
    cout << "平均FPS: " << avgFps << endl;
    
    return 0;
}
