# 独立测试 Makefile
CXX = g++
CXXFLAGS = -std=c++17 -Wall -O2

# 包含目录
INCLUDES = -I. -I/usr/include/opencv4 -I/usr/include/pcl-1.11 -I/usr/include/eigen3

# 库文件
LIBS = -lopencv_core -lopencv_imgproc -lopencv_imgcodecs -lopencv_calib3d -lopencv_highgui \
       -lpcl_common -lpcl_io -lpcl_filters -lpcl_segmentation \
       -fopenmp

# 源文件
SOURCES = test_lightstereo_standalone.cpp \
          src/inference/lightstereo_inference.cpp \
          src/inference/stereo_inference.cpp

# 目标文件
TARGET = test_lightstereo

# 编译规则
$(TARGET): $(SOURCES)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $(TARGET) $(SOURCES) $(LIBS)

# 清理
clean:
	rm -f $(TARGET)

# 运行测试
test: $(TARGET)
	./$(TARGET)

.PHONY: clean test
