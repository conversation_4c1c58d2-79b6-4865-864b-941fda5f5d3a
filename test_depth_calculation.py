#!/usr/bin/env python3
"""
深度计算测试工具
用于验证深度计算的正确性
"""

import cv2 as cv
import numpy as np
import os

def load_camera_parameters():
    """加载相机参数"""
    try:
        # 加载左相机内参
        with open("camera_parameters/camera0_intrinsics.dat", 'r') as f:
            lines = f.readlines()
        
        camera_matrix_left = []
        distortion_left = []
        reading_intrinsic = False
        reading_distortion = False
        
        for line in lines:
            if "intrinsic:" in line:
                reading_intrinsic = True
                reading_distortion = False
                continue
            elif "distortion:" in line:
                reading_intrinsic = False
                reading_distortion = True
                continue
                
            if reading_intrinsic:
                camera_matrix_left.append([float(x) for x in line.split()])
            elif reading_distortion:
                distortion_left = [float(x) for x in line.split()]
        
        camera_matrix_left = np.array(camera_matrix_left)
        distortion_left = np.array([distortion_left])
        
        # 加载右相机内参
        with open("camera_parameters/camera1_intrinsics.dat", 'r') as f:
            lines = f.readlines()
        
        camera_matrix_right = []
        distortion_right = []
        reading_intrinsic = False
        reading_distortion = False
        
        for line in lines:
            if "intrinsic:" in line:
                reading_intrinsic = True
                reading_distortion = False
                continue
            elif "distortion:" in line:
                reading_intrinsic = False
                reading_distortion = True
                continue
                
            if reading_intrinsic:
                camera_matrix_right.append([float(x) for x in line.split()])
            elif reading_distortion:
                distortion_right = [float(x) for x in line.split()]
        
        camera_matrix_right = np.array(camera_matrix_right)
        distortion_right = np.array([distortion_right])
        
        # 加载外参
        with open("camera_parameters/camera1_rot_trans.dat", 'r') as f:
            lines = f.readlines()
        
        rotation_matrix = []
        translation_vector = []
        reading_rotation = False
        reading_translation = False
        
        for line in lines:
            if "R:" in line:
                reading_rotation = True
                reading_translation = False
                continue
            elif "T:" in line:
                reading_rotation = False
                reading_translation = True
                continue
                
            if reading_rotation and line.strip():
                rotation_matrix.append([float(x) for x in line.split()])
            elif reading_translation and line.strip():
                translation_vector.append(float(line.strip()))
        
        rotation_matrix = np.array(rotation_matrix)
        translation_vector = np.array(translation_vector).reshape(3, 1)
        
        return camera_matrix_left, distortion_left, camera_matrix_right, distortion_right, rotation_matrix, translation_vector
        
    except Exception as e:
        print(f"加载相机参数时出错: {e}")
        return None, None, None, None, None, None

def test_depth_calculation():
    """测试深度计算"""
    print("=== 深度计算测试 ===")
    
    # 加载参数
    params = load_camera_parameters()
    if params[0] is None:
        return
    
    cmtx_left, dist_left, cmtx_right, dist_right, R, T = params
    
    # 计算基线长度
    baseline = np.linalg.norm(T)
    print(f"基线长度: {baseline:.2f} mm")
    
    # 计算平均焦距
    fx_left = cmtx_left[0, 0]
    fy_left = cmtx_left[1, 1]
    fx_right = cmtx_right[0, 0]
    fy_right = cmtx_right[1, 1]
    
    focal_length = (fx_left + fy_left + fx_right + fy_right) / 4
    print(f"平均焦距: {focal_length:.2f} 像素")
    
    # 测试不同视差值的深度
    test_disparities = [5, 10, 20, 50, 100]
    print("\n视差-深度对应关系:")
    print("视差(像素) | 深度(mm) | 距离(cm)")
    print("-" * 35)
    
    for disp in test_disparities:
        if disp > 0:
            depth_mm = baseline * focal_length / disp
            depth_cm = depth_mm / 10
            print(f"{disp:>8} | {depth_mm:>8.1f} | {depth_cm:>7.1f}")
    
    # 分析深度精度
    print(f"\n=== 深度精度分析 ===")
    print(f"深度缩放因子: {baseline * focal_length:.2f}")
    
    # 计算视差误差对深度的影响
    disparity_error = 1.0  # 1像素的视差误差
    for disp in [10, 20, 50]:
        depth_original = baseline * focal_length / disp
        depth_with_error = baseline * focal_length / (disp + disparity_error)
        depth_error = abs(depth_original - depth_with_error)
        error_percentage = (depth_error / depth_original) * 100
        
        print(f"视差{disp}像素时，1像素视差误差导致深度误差: {depth_error:.1f}mm ({error_percentage:.1f}%)")
    
    # 检查基线长度是否合理
    print(f"\n=== 基线长度检查 ===")
    if baseline < 1.0:
        print("⚠️  基线长度过小 (< 1mm)，可能导致深度计算不准确")
        print("建议: 检查相机安装距离，确保基线长度在5-50mm范围内")
    elif baseline > 100:
        print("⚠️  基线长度过大 (> 100mm)，可能影响近距离测量精度")
    else:
        print("✅ 基线长度在合理范围内")
    
    # 检查焦距是否合理
    print(f"\n=== 焦距检查 ===")
    if focal_length < 100:
        print("⚠️  焦距过小，可能影响深度精度")
    elif focal_length > 2000:
        print("⚠️  焦距过大，可能影响视场角")
    else:
        print("✅ 焦距在合理范围内")

if __name__ == "__main__":
    test_depth_calculation() 