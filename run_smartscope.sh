#!/bin/bash

# 设置项目根目录路径
PROJECT_ROOT="/home/<USER>/App/Qt/SmartScope"

# 输出启动信息
echo "启动SmartScope应用程序..."
echo "项目根目录: $PROJECT_ROOT"

# 进入项目根目录
cd "$PROJECT_ROOT"
echo "当前工作目录: $(pwd)"

# 设置库路径
export LD_LIBRARY_PATH=$PWD:$LD_LIBRARY_PATH
echo "设置库路径: $LD_LIBRARY_PATH"

# 定义应用程序名称
APP_NAME="SmartScopeQt"

# 检查是否已经有实例在运行
PID=$(pgrep -x "$APP_NAME")
if [ -n "$PID" ]; then
    echo "发现SmartScope已在运行，进程ID: $PID，正在切换到前台..."
    
    # 获取窗口ID
    WINDOW_ID=$(wmctrl -lp | grep "$PID" | awk '{print $1}')
    
    if [ -n "$WINDOW_ID" ]; then
        # 使用wmctrl将窗口切换到前台
        wmctrl -ia "$WINDOW_ID"
        echo "已切换到前台"
    else
        echo "无法找到窗口，可能需要安装wmctrl: sudo apt-get install wmctrl"
    fi
    
    exit 0
fi

# 检查可执行文件是否存在
if [ -f "./$APP_NAME" ]; then
    echo "找到可执行文件，启动应用程序..."
    # 运行程序
    ./$APP_NAME "$@"
else
    echo "错误: 可执行文件不存在! (./$APP_NAME)"
    echo "请确保程序已经编译完成，并在正确的目录下运行此脚本。"
    exit 1
fi 